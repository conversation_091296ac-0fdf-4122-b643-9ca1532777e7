replicaCount: 9

image:
  repo: registry.imgdot.dev/roum-web
  tag: "425a996"

imagePullSecrets:
  registry: registry.imgdot.dev
  username: axu
  password: ""

# Configuration parameters for Ingress resource
ingress:
  acme: false
  annotations:
    nginx.ingress.kubernetes.io/from-to-www-redirect: "true"
  enabled: true
  hosts:
    - host: "rouman5.com"
      paths: ["/"]

nextAuthURL: https://rouman5.com
dbUrl: "postgresql://postgres:password@localhost:5432/rouv?schema=public"
s3Bucket: rouman
# imgProxyUrl: https://rv.roucdn.link
s3ProxyUrl: https://r5.rmcdn5.xyz/m,https://r5.rmcdn6.xyz/m,https://r5.rmcdn7.xyz/m,https://r5.rmcdn8.xyz/m,https://r5.rmcdn9.xyz/m
s3ProxyKey: xxx
s3ProxySalt: yyy
authSecret: xxx
authKeycloakSecret: yyy
redisReadUrl: 
redisWriteUrl: 
redisViewChannel: rouman-view
cacheDuration: 10
# busyMode: 5-7,14-17
clickhouseUrl: http://clickhouse.db.svc.cluster.local:8123
clickhouseUser: default
clickhouseDatabase: rouman
meiliUrl: http://meilisearch.db.svc.cluster.local:7700
meiliBookIndex: rm_book
