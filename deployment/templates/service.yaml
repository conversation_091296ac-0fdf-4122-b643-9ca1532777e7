apiVersion: v1
kind: Service
metadata:
  name: {{ template "app.fullname" . }}
  labels:
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}"
    app: {{ .Release.Name | quote }}
spec:
  selector:
    app: {{ template "app.fullname" . }}
  ports:
  - name: http
    port: 80
    protocol: TCP
    targetPort: 3000
  # - name: metrics
  #   port: 8081
  #   protocol: TCP
  #   targetPort: 8081
  type: {{ default "ClusterIP" .Values.serviceType }}
