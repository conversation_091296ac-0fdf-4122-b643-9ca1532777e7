apiVersion: {{ .Values.apiVersion }}
kind: Deployment
metadata:
  name: {{ template "app.fullname" . }}
  labels:
    heritage: {{ .Release.Service | quote }}
    release: {{ .Release.Name | quote }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}"
    app: {{ template "app.fullname" . }}
  annotations: {{ toYaml .Values.deployment.annotations | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ template "app.fullname" . }}
  template:
    metadata:
      labels:
        app: {{ template "app.fullname" . }}
        release: {{ .Release.Name | quote }}
      annotations: {{ toYaml .Values.pod.annotations | nindent 8 }}
    spec:
      affinity: {{ toYaml .Values.affinity | nindent 8 }}
      tolerations: {{ toYaml .Values.tolerations | nindent 8 }}
      nodeSelector: {{ toYaml .Values.nodeSelector | nindent 8 }}
{{- if .Values.imagePullSecrets }}
      imagePullSecrets:
      - name: "{{ .Release.Name }}-docker-registry-secret"
{{- end }}
      containers:
      - name: "rouv-web"
        image: "{{ .Values.image.repo }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        env:
        - name: DATABASE_PRIMARY_URL
          valueFrom:
            secretKeyRef:
              name: {{ template "app.fullname" . }}-secrets
              key: dbPrimaryUrl
        - name: DATABASE_REPLICA_URL
          valueFrom:
            secretKeyRef:
              name: {{ template "app.fullname" . }}-secrets
              key: dbReplicaUrl
        - name: S3_BUCKET
          value: {{ .Values.s3Bucket| quote}}
        - name: IMGPROXY_URL
          value: {{ .Values.imgProxyUrl | quote}}
        - name: S3PROXY_URL
          value: {{ .Values.s3ProxyUrl| quote}}
        - name: S3PROXY_KEY
          valueFrom:
            secretKeyRef:
              name: {{ template "app.fullname" . }}-secrets
              key: s3ProxyKey
        - name: S3PROXY_SALT
          valueFrom:
            secretKeyRef:
              name: {{ template "app.fullname" . }}-secrets
              key: s3ProxySalt
        - name: NEXTAUTH_URL
          value: {{ .Values.nextAuthURL | quote }}
        - name: AUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ template "app.fullname" . }}-secrets
              key: authSecret
        - name: AUTH_KEYCLOAK_ID
          value: rouman-web
        - name: AUTH_KEYCLOAK_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ template "app.fullname" . }}-secrets
              key: authKeycloakSecret
        - name: AUTH_KEYCLOAK_ISSUER
          value: https://auth.rou.one/auth/realms/rou
        - name: CLICKHOUSE_URL
          value: {{ .Values.clickhouseUrl | quote}}
        - name: CLICKHOUSE_DATABASE
          value: {{ .Values.clickhouseDatabase | quote }}
        - name: CLICKHOUSE_USER
          value: {{ .Values.clickhouseUser | quote }}
        - name: CLICKHOUSE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ template "app.fullname" . }}-secrets
              key: clickhousePassword
        - name: REDIS_READ_URL
          valueFrom:
            secretKeyRef:
              name: {{ template "app.fullname" . }}-secrets
              key: redisReadUrl
        - name: REDIS_WRITE_URL
          valueFrom:
            secretKeyRef:
              name: {{ template "app.fullname" . }}-secrets
              key: redisWriteUrl
        - name: REDIS_VIEW_CHANNEL
          value: {{ .Values.redisViewChannel | quote}}
        - name: MEILI_URL
          value: {{ .Values.meiliUrl | quote}}
        - name: MEILI_API_KEY
          value: {{ .Values.meiliApiKey | quote}}
        - name: MEILI_BOOK_INDEX
          value: {{ .Values.meiliBookIndex | quote}}
        - name: IMG_SCRAMBLE_RATIO
          value: "3"
        - name: CACHE_DURATION
          value: {{ .Values.cacheDuration | quote }}
        livenessProbe:
          httpGet:
            path: /
            port: 3000
            scheme: HTTP
          {{- with .Values.livenessProbe }}
          initialDelaySeconds: {{ .initialDelaySeconds | default 10 }}
          timeoutSeconds: {{ .timeoutSeconds | default 5}}
          periodSeconds: {{ .periodSeconds | default 10 }}
          successThreshold: {{ .successThreshold | default 1 }}
          failureThreshold: {{ .failureThreshold | default 5 }}
          {{- end }}
        readinessProbe:
          httpGet:
            path: /home
            port: 3000
            scheme: HTTP
          {{- with .Values.readinessProbe }}
          initialDelaySeconds: {{ .initialDelaySeconds | default 10 }}
          timeoutSeconds: {{ .timeoutSeconds | default 5}}
          periodSeconds: {{ .periodSeconds | default 10 }}
          successThreshold: {{ .successThreshold | default 1 }}
          failureThreshold: {{ .failureThreshold | default 5 }}
          {{- end }}
        lifecycle:
          preStop:
            exec:
              command: ["/bin/sleep","5"]
        resources:
          limits:
            memory: 512Gi
          requests:
            cpu: 100m
            memory: 256Mi