{{- if .Values.imagePullSecrets }}
apiVersion: v1
data:
  .dockerconfigjson: {{ template "app.imagePullSecrets" . }}
kind: Secret
metadata:
  labels:
    app: {{ template "app.name" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}"
    release: {{ .Release.Name | quote }}
    heritage: {{ .Release.Service | quote }}
  name: "{{ .Release.Name }}-docker-registry-secret"
type: kubernetes.io/dockerconfigjson
{{- end }}
