apiVersion: v1
kind: Secret
metadata:
  name: {{ template "app.fullname" . }}-secrets
  labels:
    app: {{ template "app.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
type: kubernetes.io/Opaque
data:
  dbPrimaryUrl: {{ .Values.dbPrimaryUrl | b64enc | quote }}
  dbReplicaUrl: {{ .Values.dbReplicaUrl | b64enc | quote }}
  s3ProxyKey: {{ .Values.s3ProxyKey | b64enc | quote }}
  s3ProxySalt: {{ .Values.s3ProxySalt | b64enc | quote }}
  authSecret: {{ .Values.authSecret | b64enc | quote }}
  authKeycloakSecret: {{ .Values.authKeycloakSecret | b64enc | quote }}
  redisReadUrl: {{ .Values.redisReadUrl | b64enc | quote }}
  redisWriteUrl: {{ .Values.redisWriteUrl | b64enc | quote }}
  clickhousePassword: {{ .Values.clickhousePassword | b64enc | quote }}
  meiliApiKey: {{ .Values.meiliApiKey | b64enc | quote }}