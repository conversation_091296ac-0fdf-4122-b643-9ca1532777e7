pipeline {
    agent any
    stages {
        stage('Build') {
            environment { 
                IMAGE_TAG = sh (script: 'git describe --always', returnStdout: true).trim()
            }

            steps {
                withCredentials([file(credentialsId: 'npmrc', variable: 'npmrc_content')]) {
                    writeFile file: '.npmrc', text: readFile(npmrc_content)
                }

                sh "echo $IMAGE_TAG"
                sh label: "docker build", script: "docker build . -t registry.imgdot.dev/roum-web:$IMAGE_TAG"

                withCredentials([usernamePassword(credentialsId: 'docker-imgdot', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
                    // available as an env variable, but will be masked if you try to print it out any which way
                    // note: single quotes prevent Groovy interpolation; expansion is by Bourne Shell, which is what you want
                    sh 'docker login registry.imgdot.dev -u="${DOCKER_USERNAME}" -p="${DOCKER_PASSWORD}"'
                    sh "docker push registry.imgdot.dev/roum-web:$IMAGE_TAG"
                }
            }
        }
    }
}