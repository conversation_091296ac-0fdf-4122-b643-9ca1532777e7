This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

## prisma generate client

```
npx prisma generate
```

## start up postgres locally

```
podman run -d --name postgres -p 5432:5432 -e POSTGRES_PASSWORD=password postgres:17
```

# Start Redis locally

```
podman run --name redis -p 16379:6379 -d docker.io/redis:7.4
```

# install

```
helm upgrade -i roum-web -f roum-web_prod.yaml -f roum-web-secret_prod.yaml ./deployment -n rouman
helm upgrade -i roum-web2 -f roum-web_prod.yaml -f roum-web_prod2.yaml -f roum-web-secret_prod.yaml ./deployment -n rouman
helm upgrade -i roum-web3 -f roum-web_prod.yaml -f roum-web_prod3.yaml -f roum-web-secret_prod.yaml ./deployment -n rouman
```

## test deploy

```
helm upgrade -i roum-web-test -f roum-web_prod.yaml -f roum-web_test.yaml -f roum-web-secret_prod.yaml ./deployment -n rouman
```

# start meilisearch locally

```
podman run -d \
    --name meilisearch \
    -p 7700:7700 \
    -v /Users/<USER>/prj/rouone/data/meili_data:/meili_data:Z \
    -e MEILI_MASTER_KEY='masterKey' \
    getmeili/meilisearch:latest
```

# start clickhouse locally

```
podman run -d --name clickhouse \
 -p 8123:8123 \
 -p 9000:9000 \
 -v /Users/<USER>/prj/rouone/data/clickhouse:/var/lib/clickhouse:Z \
 clickhouse/clickhouse-server:latest
```

# CI/CD with GitHub Actions

This project uses GitHub Actions for continuous integration and deployment. The workflow includes:

1. Building and pushing the Docker image to the registry
2. Automatic deployment to the test environment
3. Manual approval step before production deployment
4. Deployment to all three production instances after approval
5. Rollback capability for quick recovery

## Deployment Strategy

The GitHub Actions workflow follows this deployment strategy:

- **Test First**: All changes are deployed to the test environment first for validation
- **Manual Approval**: Production deployments require manual approval
- **Multiple Production Instances**: Deploys to all three production applications
- **Rollback Support**: Quick rollback to previous versions if issues occur

## Manual Deployment

You can manually trigger a deployment or rollback:

1. Go to the "Actions" tab in the GitHub repository
2. Select the "Build and Deploy" workflow
3. Click "Run workflow"
4. Select the branch, environment (test/production), and action (deploy/rollback)
5. Click "Run workflow"

For more details on the GitHub Actions setup, see [.github/README.md](.github/README.md).

## Kubernetes Deployment

The application can be deployed to Kubernetes using the following methods:

### Using GitHub Actions (Recommended)

Push your changes to the main branch, and the GitHub Actions workflow will build and deploy to the test environment. After testing, manually trigger a production deployment.

### Manual Deployment

You can still deploy manually using Helm:

```
# Test environment
helm upgrade -i roum-web-test -f roum-web_prod.yaml -f roum-web_test.yaml -f roum-web-secret_prod.yaml ./deployment -n rouman

# Production environments
helm upgrade -i roum-web -f roum-web_prod.yaml -f roum-web-secret_prod.yaml ./deployment -n rouman
helm upgrade -i roum-web2 -f roum-web_prod.yaml -f roum-web_prod2.yaml -f roum-web-secret_prod.yaml ./deployment -n rouman
helm upgrade -i roum-web3 -f roum-web_prod.yaml -f roum-web_prod3.yaml -f roum-web-secret_prod.yaml ./deployment -n rouman
```
