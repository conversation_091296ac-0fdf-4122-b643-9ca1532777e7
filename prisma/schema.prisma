// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_PRIMARY_URL")
}

model Account {
  id                 String  @id @default(cuid())
  userId             String
  type               String
  provider           String
  providerAccountId  String
  refresh_token      String?
  access_token       String?
  expires_at         Int?
  token_type         String?
  scope              String?
  id_token           String?
  session_state      String?
  oauth_token_secret String?
  oauth_token        String?
  refresh_expires_in Int?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String        @id @default(cuid())
  name          String?
  email         String?       @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]
  bookShelves   BookShelf[]
  readHistory   ReadHistory[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Book {
  id               String         @id @default(cuid())
  name             String         @unique
  description      String
  alias            String[]
  tags             String[]
  author           String
  rating           Float?
  continued        Boolean
  viewCount        Int            @default(0)
  likeCount        Int            @default(0)
  archived         Boolean        @default(false)
  archivedAt       DateTime?
  publish          Boolean        @default(false)
  region           String
  createdAt        DateTime       @default(now())
  updatedAt        DateTime?
  resources        BookResource[] @relation(name: "resources")
  activeResource   BookResource?  @relation(fields: [activeResourceId], references: [id])
  activeResourceId String?        @unique
  lockResource     Boolean        @default(false)
  bookShelves      BookShelf[]

  readHistory ReadHistory[]

  @@index([publish, continued, updatedAt])
  @@index([publish, tags, updatedAt])
  @@index([publish, rating, updatedAt])
}

model BookResource {
  id           String    @id @default(cuid())
  description  String
  author       String
  continued    Boolean
  tags         String[]
  chapterNames String[]
  resourceKey  String
  resourceRef  String    @unique
  folderPath   String
  createdAt    DateTime  @default(now())
  updatedAt    DateTime?
  book         Book      @relation(fields: [bookId], references: [id], name: "resources")
  bookId       String
  activeBook   Book?
  chapters     Chapter[]

  @@index([continued, updatedAt])
}

model Chapter {
  id          String   @id @default(cuid())
  name        String
  ind         Int
  resourceRef String
  imagePaths  String[]
  active      Boolean  @default(true)

  bookResource   BookResource  @relation(fields: [bookResourceId], references: [id])
  bookResourceId String
  createdAt      DateTime      @default(now())
  ReadHistory    ReadHistory[]
  BookShelf      BookShelf[]

  @@unique([bookResourceId, ind, active])
  @@unique([bookResourceId, resourceRef])
  @@index([resourceRef])
}

model BookShelf {
  id     String @id @default(cuid())
  book   Book   @relation(fields: [bookId], references: [id])
  bookId String

  latestChapterIndex Int      @default(0)
  lastRead           Chapter  @relation(fields: [chapterId], references: [id])
  chapterId          String
  user               User     @relation(fields: [userId], references: [id])
  userId             String
  createdAt          DateTime @default(now())

  @@unique([bookId, userId])
  @@index([userId, createdAt])
}

model ReadHistory {
  id String @id @default(cuid())

  book      Book     @relation(fields: [bookId], references: [id])
  bookId    String
  chapter   Chapter  @relation(fields: [chapterId], references: [id])
  chapterId String
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  createdAt DateTime @default(now())

  @@unique([bookId, userId, chapterId])
  @@index([userId, createdAt])
}

model Tag {
  id    String @id
  count Int    @default(0)
}
