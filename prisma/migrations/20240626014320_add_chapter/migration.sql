/*
  Warnings:

  - You are about to drop the column `chapters` on the `BookResource` table. All the data in the column will be lost.
  - Added the required column `region` to the `Book` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Book" ADD COLUMN     "lockResource" BOOLEAN NOT NULL DEFAULT false;

ALTER TABLE "Book" ADD COLUMN     "region" TEXT;
UPDATE "Book" SET "region"='韓國';
ALTER TABLE "Book" ALTER COLUMN "region" SET NOT NULL;

-- AlterTable
ALTER TABLE "BookResource" RENAME COLUMN "chapters" TO "chapterNames";

-- CreateTable
CREATE TABLE "Chapter" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "ind" INTEGER NOT NULL,
    "resourceRef" TEXT NOT NULL,
    "imagePaths" TEXT[],
    "active" BOOLEAN NOT NULL DEFAULT true,
    "bookResourceId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Chapter_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Chapter_resourceRef_idx" ON "Chapter"("resourceRef");

-- CreateIndex
CREATE UNIQUE INDEX "Chapter_bookResourceId_ind_active_key" ON "Chapter"("bookResourceId", "ind", "active");

-- CreateIndex
CREATE UNIQUE INDEX "Chapter_bookResourceId_resourceRef_key" ON "Chapter"("bookResourceId", "resourceRef");

-- AddForeignKey
ALTER TABLE "Chapter" ADD CONSTRAINT "Chapter_bookResourceId_fkey" FOREIGN KEY ("bookResourceId") REFERENCES "BookResource"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
