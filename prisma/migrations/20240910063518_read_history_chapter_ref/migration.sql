/*
  Warnings:

  - You are about to drop the column `chapterIndex` on the `ReadHistory` table. All the data in the column will be lost.
  - You are about to drop the column `chapterName` on the `ReadHistory` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[bookId,userId,chapterId]` on the table `ReadHistory` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `chapterId` to the `ReadHistory` table without a default value. This is not possible if the table is not empty.

*/
TRUNCATE "ReadHistory";

-- DropIndex
DROP INDEX "ReadHistory_bookId_userId_chapterIndex_key";

-- AlterTable
ALTER TABLE "ReadHistory" DROP COLUMN "chapterIndex",
DROP COLUMN "chapterName",
ADD COLUMN     "chapterId" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "ReadHistory_bookId_userId_chapterId_key" ON "ReadHistory"("bookId", "userId", "chapterId");

-- AddForeignKey
ALTER TABLE "ReadHistory" ADD CONSTRAINT "ReadHistory_chapterId_fkey" FOREIGN KEY ("chapterId") REFERENCES "Chapter"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
