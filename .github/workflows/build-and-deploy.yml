name: Build and Deploy

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'test'
        type: choice
        options:
          - production
          - test
      action:
        description: 'Action to perform'
        required: true
        default: 'deploy'
        type: choice
        options:
          - deploy
          - rollback

env:
  REGISTRY: registry.imgdot.dev
  IMAGE_NAME: roum-web

jobs:
  build:
    runs-on: self-hosted
    outputs:
      image_tag: ${{ steps.set-image-tag.outputs.image_tag }}
      previous_tag: ${{ steps.get-previous-tag.outputs.previous_tag }}
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # Add step to check and configure Docker for Windows if needed
      - name: Configure Docker for Windows
        shell: pwsh
        run: |
          # Check if running on Windows
          if ($IsWindows -or $env:OS -like "*Windows*") {
            Write-Host "Running on Windows, checking Docker configuration..."
            
            # Check if Docker is running
            try {
              $dockerInfo = docker info 2>&1
              if ($LASTEXITCODE -ne 0) {
                Write-Error "Docker is not running. Please start Docker Desktop."
                exit 1
              }
            } catch {
              Write-Error "Docker command failed. Make sure Docker Desktop is installed and running."
              exit 1
            }
            
            # Try to set Docker host to TCP if pipe access fails
            try {
              $buildxVersion = docker buildx version 2>&1
              if ($LASTEXITCODE -ne 0) {
                Write-Host "Setting DOCKER_HOST to use TCP instead of pipe..."
                $env:DOCKER_HOST = "tcp://localhost:2375"
                echo "DOCKER_HOST=tcp://localhost:2375" | Out-File -FilePath $env:GITHUB_ENV -Append
                
                # Check if that worked
                $buildxVersion = docker buildx version 2>&1
                if ($LASTEXITCODE -ne 0) {
                  Write-Warning "Docker Buildx still not accessible. Will try to use Docker CLI directly."
                }
              }
            } catch {
              Write-Warning "Error checking Docker Buildx. Will try to use Docker CLI directly."
            }
          }

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        continue-on-error: true
        id: buildx

      - name: Generate image tag
        id: set-image-tag
        shell: pwsh
        run: |
          # Use git describe --always for consistent tag format
          echo "image_tag=$(git describe --always)" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
      
      - name: Get previous image tag for rollback
        id: get-previous-tag
        shell: pwsh
        run: |
          # Get the previous tag for rollback purposes
          $tags = $(git tag --sort=-committerdate)
          if ($tags -and $tags.Count -gt 1) {
            echo "previous_tag=$($tags[1])" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          } else {
            echo "previous_tag=latest" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          }

      - name: Create .npmrc file
        run: |
          echo "${{ secrets.NPMRC_CONTENT }}" > .npmrc

      - name: Log in to Docker registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      # Check if image already exists
      - name: Check if image exists
        id: check-image
        shell: pwsh
        run: |
          $imageTag = "${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.set-image-tag.outputs.image_tag }}"
          try {
            $manifest = docker manifest inspect $imageTag 2>&1
            if ($LASTEXITCODE -eq 0) {
              Write-Host "Image $imageTag already exists, skipping build"
              echo "image_exists=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            } else {
              Write-Host "Image $imageTag does not exist, will build"
              echo "image_exists=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            }
          } catch {
            Write-Host "Error checking image, assuming it does not exist"
            echo "image_exists=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          }
          # Always exit successfully since we handled all cases
          exit 0

      # Skip build if this is a rollback or if image already exists
      - name: Check if build should be skipped
        id: check-skip-build
        shell: pwsh
        run: |
          if ("${{ github.event.inputs.action }}" -eq "rollback" -or "${{ steps.check-image.outputs.image_exists }}" -eq "true") {
            echo "skip_build=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          } else {
            echo "skip_build=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          }

      # Use conditional to either use Buildx or fall back to Docker CLI
      - name: Build and push Docker image with Buildx
        if: steps.buildx.outcome == 'success' && steps.check-skip-build.outputs.skip_build != 'true'
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.set-image-tag.outputs.image_tag }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      # Fallback to standard Docker commands if Buildx fails
      - name: Build and push Docker image with CLI
        if: steps.buildx.outcome != 'success' && steps.check-skip-build.outputs.skip_build != 'true'
        run: |
          echo "Using Docker CLI instead of Buildx"
          docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.set-image-tag.outputs.image_tag }} .
          docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.set-image-tag.outputs.image_tag }}

  # Always deploy to test environment first
  deploy-test:
    needs: [build]
    runs-on: self-hosted
    if: github.event.inputs.action != 'rollback' || github.event.inputs.environment == 'test'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        
      - name: Configure kubectl
        shell: pwsh
        run: |
          New-Item -ItemType Directory -Path "$HOME/.kube" -Force
          [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String("${{ secrets.KUBE_CONFIG }}")) | Out-File -FilePath "$HOME/.kube/config" -Encoding utf8
          
          # Try to set permissions but continue if it fails
          try {
            # Set permissions equivalent to chmod 600
            $acl = Get-Acl -Path "$HOME/.kube/config"
            $acl.SetAccessRuleProtection($true, $false)
            $rule = New-Object System.Security.AccessControl.FileSystemAccessRule("$env:USERNAME", "Read, Write", "Allow")
            $acl.SetAccessRule($rule)
            Set-Acl -Path "$HOME/.kube/config" -AclObject $acl
            Write-Host "Successfully set permissions on kubeconfig file"
          } catch {
            Write-Warning "Unable to set permissions on kubeconfig file: $_"
            Write-Host "Continuing without setting permissions. If kubectl fails, you may need to run the workflow with elevated privileges."
          }
          
          # Verify the config file exists
          if (Test-Path "$HOME/.kube/config") {
            Write-Host "Kubeconfig file created successfully"
          } else {
            Write-Error "Failed to create kubeconfig file"
            exit 1
          }

      - name: Create secret file
        shell: pwsh
        run: |
          # Use a temporary variable with single quotes to avoid PowerShell interpreting special characters
          $secretContent = '${{ secrets.ROUM_WEB_SECRET_PROD }}'
          
          # Write the content directly to a file using .NET methods to avoid PowerShell interpretation
          [System.IO.File]::WriteAllText("roum-web-secret_prod.yaml", $secretContent)
          
          # Verify the file was created
          if (Test-Path "roum-web-secret_prod.yaml") {
            Write-Host "Secret file created successfully"
            
            # Debug: Check file size to verify content was written (don't output content for security)
            $fileInfo = Get-Item "roum-web-secret_prod.yaml"
            Write-Host "Secret file size: $($fileInfo.Length) bytes"
          } else {
            Write-Error "Failed to create secret file"
            exit 1
          }

      - name: Deploy or Rollback to Test Environment
        shell: pwsh
        run: |
          $NAMESPACE = "rouman"
          $DEPLOYMENT_NAME = "roum-web-test"
          
          # Determine if this is a deploy or rollback
          $ACTION = "${{ github.event.inputs.action }}"
          if (-not $ACTION) {
            $ACTION = "deploy"
          }
          
          # Update the image tag in the deployment file
          if ($ACTION -eq "deploy") {
            $IMAGE_TAG = "${{ needs.build.outputs.image_tag }}"
            Write-Host "Deploying version $IMAGE_TAG to test environment"
          } else {
            $IMAGE_TAG = "${{ needs.build.outputs.previous_tag }}"
            Write-Host "Rolling back to version $IMAGE_TAG in test environment"
          }
          
          (Get-Content -Path roum-web_prod.yaml) -replace 'tag: ".*"', "tag: `"$IMAGE_TAG`"" | Set-Content -Path roum-web_prod.yaml
          
          # Deploy using helm upgrade -i
          helm upgrade -i $DEPLOYMENT_NAME -f roum-web_prod.yaml -f roum-web_test.yaml -f roum-web-secret_prod.yaml ./deployment -n $NAMESPACE
          
          # Try to find the correct deployment name
          $CHART_NAME = "roumweb"  # From Chart.yaml
          $ACTUAL_DEPLOYMENT_NAME = "$DEPLOYMENT_NAME-$CHART_NAME"
          Write-Host "Looking for deployment: $ACTUAL_DEPLOYMENT_NAME"
          
          # Check if the deployment exists
          $deploymentExists = kubectl get deployment $ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE 2>&1
          if ($LASTEXITCODE -eq 0) {
            Write-Host "Found deployment: $ACTUAL_DEPLOYMENT_NAME"
            # Wait for rollout to complete
            kubectl rollout status deployment/$ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE --timeout=300s
          } else {
            # Try alternative naming pattern
            $ACTUAL_DEPLOYMENT_NAME = "$DEPLOYMENT_NAME"
            Write-Host "Trying alternative deployment name: $ACTUAL_DEPLOYMENT_NAME"
            $deploymentExists = kubectl get deployment $ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE 2>&1
            if ($LASTEXITCODE -eq 0) {
              Write-Host "Found deployment: $ACTUAL_DEPLOYMENT_NAME"
              # Wait for rollout to complete
              kubectl rollout status deployment/$ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE --timeout=300s
            } else {
              # List all deployments in the namespace to help debug
              Write-Host "Could not find deployment. Listing all deployments in namespace $NAMESPACE`:"
              kubectl get deployments -n $NAMESPACE
              
              # Don't fail the workflow, as Helm reported success
              Write-Warning "Could not find deployment to check rollout status, but Helm reported successful deployment"
            }
          }

      - name: Verify test deployment
        shell: pwsh
        run: |
          $NAMESPACE = "rouman"
          $DEPLOYMENT_NAME = "roum-web-test"
          
          # Try to find the correct deployment name (same logic as in the previous step)
          $CHART_NAME = "roumweb"  # From Chart.yaml
          $ACTUAL_DEPLOYMENT_NAME = "$DEPLOYMENT_NAME-$CHART_NAME"
          
          # Check if the deployment exists with the first naming pattern
          $deploymentExists = kubectl get deployment $ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE 2>&1
          if ($LASTEXITCODE -ne 0) {
            # Try alternative naming pattern
            $ACTUAL_DEPLOYMENT_NAME = "$DEPLOYMENT_NAME"
          }
          
          # Get deployment status using the actual deployment name
          Write-Host "Verifying deployment: $ACTUAL_DEPLOYMENT_NAME"
          kubectl get deployment $ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE -o wide
          
          # Check if pods are running
          Write-Host "Checking pod status..."
          kubectl get pods -n $NAMESPACE -l app=$ACTUAL_DEPLOYMENT_NAME -o wide
          
          Write-Host "✅ Test deployment completed. Please verify the application in the test environment before proceeding to production."

  # Manual approval step before deploying to production
  approve-production:
    needs: [build, deploy-test]
    runs-on: self-hosted
    if: github.event.inputs.environment == 'production'
    environment: production-approval
    steps:
      - name: Approval notification
        run: echo "Deployment to production has been approved"

  # Deploy to all three production instances
  deploy-production:
    needs: [build, approve-production]
    runs-on: self-hosted
    if: github.event.inputs.environment == 'production'
    strategy:
      matrix:
        app: [
          { name: "roum-web", config: "" },
          { name: "roum-web2", config: "roum-web_prod2.yaml" },
          { name: "roum-web3", config: "roum-web_prod3.yaml" }
        ]
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        
      - name: Configure kubectl
        shell: pwsh
        run: |
          New-Item -ItemType Directory -Path "$HOME/.kube" -Force
          [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String("${{ secrets.KUBE_CONFIG }}")) | Out-File -FilePath "$HOME/.kube/config" -Encoding utf8
          
          # Try to set permissions but continue if it fails
          try {
            # Set permissions equivalent to chmod 600
            $acl = Get-Acl -Path "$HOME/.kube/config"
            $acl.SetAccessRuleProtection($true, $false)
            $rule = New-Object System.Security.AccessControl.FileSystemAccessRule("$env:USERNAME", "Read, Write", "Allow")
            $acl.SetAccessRule($rule)
            Set-Acl -Path "$HOME/.kube/config" -AclObject $acl
            Write-Host "Successfully set permissions on kubeconfig file"
          } catch {
            Write-Warning "Unable to set permissions on kubeconfig file: $_"
            Write-Host "Continuing without setting permissions. If kubectl fails, you may need to run the workflow with elevated privileges."
          }
          
          # Verify the config file exists
          if (Test-Path "$HOME/.kube/config") {
            Write-Host "Kubeconfig file created successfully"
          } else {
            Write-Error "Failed to create kubeconfig file"
            exit 1
          }

      - name: Create secret file
        shell: pwsh
        run: |
          # Use a temporary variable with single quotes to avoid PowerShell interpreting special characters
          $secretContent = '${{ secrets.ROUM_WEB_SECRET_PROD }}'
          
          # Write the content directly to a file using .NET methods to avoid PowerShell interpretation
          [System.IO.File]::WriteAllText("roum-web-secret_prod.yaml", $secretContent)
          
          # Verify the file was created
          if (Test-Path "roum-web-secret_prod.yaml") {
            Write-Host "Secret file created successfully"
            
            # Debug: Check file size to verify content was written (don't output content for security)
            $fileInfo = Get-Item "roum-web-secret_prod.yaml"
            Write-Host "Secret file size: $($fileInfo.Length) bytes"
          } else {
            Write-Error "Failed to create secret file"
            exit 1
          }

      - name: Deploy or Rollback to Production
        shell: pwsh
        run: |
          $NAMESPACE = "rouman"
          $DEPLOYMENT_NAME = "${{ matrix.app.name }}"
          $EXTRA_CONFIG = "${{ matrix.app.config }}"
          
          # Determine if this is a deploy or rollback
          $ACTION = "${{ github.event.inputs.action }}"
          if (-not $ACTION) {
            $ACTION = "deploy"
          }
          
          # Update the image tag in the deployment file
          if ($ACTION -eq "deploy") {
            $IMAGE_TAG = "${{ needs.build.outputs.image_tag }}"
            Write-Host "Deploying version $IMAGE_TAG to production environment ($DEPLOYMENT_NAME)"
          } else {
            $IMAGE_TAG = "${{ needs.build.outputs.previous_tag }}"
            Write-Host "Rolling back to version $IMAGE_TAG in production environment ($DEPLOYMENT_NAME)"
          }
          
          (Get-Content -Path roum-web_prod.yaml) -replace 'tag: ".*"', "tag: `"$IMAGE_TAG`"" | Set-Content -Path roum-web_prod.yaml
          
          # Deploy using helm upgrade -i with the appropriate config files
          if ($EXTRA_CONFIG) {
            Write-Host "Using extra config file: $EXTRA_CONFIG"
            helm upgrade -i $DEPLOYMENT_NAME -f roum-web_prod.yaml -f $EXTRA_CONFIG -f roum-web-secret_prod.yaml ./deployment -n $NAMESPACE
          } else {
            Write-Host "Using standard config"
            helm upgrade -i $DEPLOYMENT_NAME -f roum-web_prod.yaml -f roum-web-secret_prod.yaml ./deployment -n $NAMESPACE
          }
          
          # Try to find the correct deployment name
          $CHART_NAME = "roumweb"  # From Chart.yaml
          $ACTUAL_DEPLOYMENT_NAME = "$DEPLOYMENT_NAME-$CHART_NAME"
          Write-Host "Looking for deployment: $ACTUAL_DEPLOYMENT_NAME"
          
          # Check if the deployment exists
          $deploymentExists = kubectl get deployment $ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE 2>&1
          if ($LASTEXITCODE -eq 0) {
            Write-Host "Found deployment: $ACTUAL_DEPLOYMENT_NAME"
            # Wait for rollout to complete
            kubectl rollout status deployment/$ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE --timeout=300s
          } else {
            # Try alternative naming pattern
            $ACTUAL_DEPLOYMENT_NAME = "$DEPLOYMENT_NAME"
            Write-Host "Trying alternative deployment name: $ACTUAL_DEPLOYMENT_NAME"
            $deploymentExists = kubectl get deployment $ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE 2>&1
            if ($LASTEXITCODE -eq 0) {
              Write-Host "Found deployment: $ACTUAL_DEPLOYMENT_NAME"
              # Wait for rollout to complete
              kubectl rollout status deployment/$ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE --timeout=300s
            } else {
              # List all deployments in the namespace to help debug
              Write-Host "Could not find deployment. Listing all deployments in namespace $NAMESPACE`:"
              kubectl get deployments -n $NAMESPACE
              
              # Don't fail the workflow, as Helm reported success
              Write-Warning "Could not find deployment to check rollout status, but Helm reported successful deployment"
            }
          }

      - name: Verify production deployment
        shell: pwsh
        run: |
          $NAMESPACE = "rouman"
          $DEPLOYMENT_NAME = "${{ matrix.app.name }}"
          
          # Try to find the correct deployment name (same logic as in the previous step)
          $CHART_NAME = "roumweb"  # From Chart.yaml
          $ACTUAL_DEPLOYMENT_NAME = "$DEPLOYMENT_NAME-$CHART_NAME"
          
          # Check if the deployment exists with the first naming pattern
          $deploymentExists = kubectl get deployment $ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE 2>&1
          if ($LASTEXITCODE -ne 0) {
            # Try alternative naming pattern
            $ACTUAL_DEPLOYMENT_NAME = "$DEPLOYMENT_NAME"
          }
          
          # Get deployment status using the actual deployment name
          Write-Host "Verifying deployment: $ACTUAL_DEPLOYMENT_NAME"
          kubectl get deployment $ACTUAL_DEPLOYMENT_NAME -n $NAMESPACE -o wide
          
          # Check if pods are running
          Write-Host "Checking pod status..."
          kubectl get pods -n $NAMESPACE -l app=$ACTUAL_DEPLOYMENT_NAME -o wide
          
          Write-Host "✅ Production deployment of $DEPLOYMENT_NAME completed successfully." 