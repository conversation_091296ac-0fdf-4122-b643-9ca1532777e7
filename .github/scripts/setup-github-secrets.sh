#!/bin/bash

# This script helps set up the required GitHub secrets for the GitHub Actions workflow
# It requires the GitHub CLI (gh) to be installed and authenticated

# Check if gh is installed
if ! command -v gh &> /dev/null; then
    echo "GitHub CLI (gh) is not installed. Please install it first:"
    echo "https://cli.github.com/manual/installation"
    exit 1
fi

# Check if gh is authenticated
if ! gh auth status &> /dev/null; then
    echo "GitHub CLI is not authenticated. Please run 'gh auth login' first."
    exit 1
fi

# Get repository name
REPO=$(gh repo view --json nameWithOwner -q .nameWithOwner)
if [ -z "$REPO" ]; then
    echo "Could not determine repository name. Please run this script from within a GitHub repository."
    exit 1
fi

echo "Setting up secrets for repository: $REPO"

# Set up Docker credentials
read -p "Enter Docker registry username: " DOCKER_USERNAME
read -sp "Enter Docker registry password: " DOCKER_PASSWORD
echo
gh secret set DOCKER_USERNAME --body="$DOCKER_USERNAME" --repo="$REPO"
gh secret set DOCKER_PASSWORD --body="$DOCKER_PASSWORD" --repo="$REPO"

# Set up .npmrc content
if [ -f ".npmrc" ]; then
    NPMRC_CONTENT=$(cat .npmrc)
    gh secret set NPMRC_CONTENT --body="$NPMRC_CONTENT" --repo="$REPO"
    echo "Set NPMRC_CONTENT from existing .npmrc file"
else
    read -p "Enter path to .npmrc file: " NPMRC_PATH
    if [ -f "$NPMRC_PATH" ]; then
        NPMRC_CONTENT=$(cat "$NPMRC_PATH")
        gh secret set NPMRC_CONTENT --body="$NPMRC_CONTENT" --repo="$REPO"
        echo "Set NPMRC_CONTENT from provided file"
    else
        echo "Could not find .npmrc file. Please set the NPMRC_CONTENT secret manually."
    fi
fi

# Set up Kubernetes config
read -p "Enter path to kubeconfig file (default: ~/.kube/config): " KUBE_CONFIG_PATH
KUBE_CONFIG_PATH=${KUBE_CONFIG_PATH:-~/.kube/config}

if [ -f "$KUBE_CONFIG_PATH" ]; then
    KUBE_CONFIG=$(cat "$KUBE_CONFIG_PATH" | base64)
    gh secret set KUBE_CONFIG --body="$KUBE_CONFIG" --repo="$REPO"
    echo "Set KUBE_CONFIG from $KUBE_CONFIG_PATH"
else
    echo "Could not find kubeconfig file. Please set the KUBE_CONFIG secret manually."
fi

# Set up roum-web-secret_prod.yaml
read -p "Enter path to roum-web-secret_prod.yaml file: " SECRET_FILE_PATH
if [ -f "$SECRET_FILE_PATH" ]; then
    # Ask if user wants to store as raw or base64
    echo "How would you like to store the secret file?"
    echo "1) Raw content (may have issues with special characters)"
    echo "2) Base64 encoded (recommended for files with special characters)"
    read -p "Enter choice [2]: " SECRET_ENCODING_CHOICE
    SECRET_ENCODING_CHOICE=${SECRET_ENCODING_CHOICE:-2}
    
    if [ "$SECRET_ENCODING_CHOICE" = "1" ]; then
        # Store as raw content
        SECRET_CONTENT=$(cat "$SECRET_FILE_PATH")
        gh secret set ROUM_WEB_SECRET_PROD --body="$SECRET_CONTENT" --repo="$REPO"
        echo "Set ROUM_WEB_SECRET_PROD from $SECRET_FILE_PATH (raw content)"
    else
        # Store as base64 encoded content
        SECRET_CONTENT_BASE64=$(cat "$SECRET_FILE_PATH" | base64)
        gh secret set ROUM_WEB_SECRET_PROD_BASE64 --body="$SECRET_CONTENT_BASE64" --repo="$REPO"
        echo "Set ROUM_WEB_SECRET_PROD_BASE64 from $SECRET_FILE_PATH (base64 encoded)"
        
        # Also update the workflow file to use the base64 version
        echo "Note: Make sure your workflow is configured to use ROUM_WEB_SECRET_PROD_BASE64 instead of ROUM_WEB_SECRET_PROD"
        echo "Example workflow step:"
        echo '
      - name: Create secret file
        shell: pwsh
        run: |
          # Decode base64 secret to avoid PowerShell interpreting special characters
          $bytes = [System.Convert]::FromBase64String("${{ secrets.ROUM_WEB_SECRET_PROD_BASE64 }}")
          [System.IO.File]::WriteAllBytes("roum-web-secret_prod.yaml", $bytes)
        '
    fi
else
    echo "Could not find roum-web-secret_prod.yaml file. Please set the ROUM_WEB_SECRET_PROD secret manually."
fi

echo "Setup complete!" 