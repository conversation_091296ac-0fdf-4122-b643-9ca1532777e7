/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: '192.168.86.*',
        port: '8000',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'img.ra*.xyz',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'r5.rmcdn*.xyz',
        pathname: '/**',
      },
    ],
  },
  output: 'standalone',
};

export default nextConfig;
