
const spot = new StripchatSpot({
    autoplay: 'all',
    userId: '71df7b2672bcea7e2d5425c79830b46adca4e9dd3e05f6bd5cdfc6c0d2287c40',
    campaignId: 'videoslider',
    //sourceId: '',
    tag: 'girls/chinese',
    showModal: 'signup',
    hideButton: '1',
    autoclose: 0,
    closeButtonDelay: 0,
    //width: 350,
    //height: 200,
});
spot.mount(document.getElementById('player-container')).then((app) => {
    setTimeout(() => {
        Object.assign(document.getElementById('player-container').firstChild.style, { width: '300px', height: '100px' })
    }, 10);

});