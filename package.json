{"name": "roum-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@clickhouse/client": "^1.12.1", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.15.0", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^6.1.0", "create-hmac": "^1.1.7", "lucide-react": "^0.471.1", "md5": "^2.3.0", "meilisearch": "^0.52.0", "next": "^15.5.2", "next-auth": "^5.0.0-beta.29", "next-themes": "0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-in-viewport": "^1.0.0-beta.8", "redis": "5.8.2", "sharp": "0.34.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.0"}, "devDependencies": {"@types/create-hmac": "^1.1.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^15.5.2", "postcss": "^8", "prisma": "^6.15.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}