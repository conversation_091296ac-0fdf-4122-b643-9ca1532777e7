"use client"

import * as React from "react"
import { Moon, Sun } from "lucide-react"
import { useTheme } from "next-themes"

import { But<PERSON> } from "@/components/ui/button"

export function ThemeToggle() {
    const { setTheme, theme, resolvedTheme } = useTheme()

    return (
        <Button
            variant="link"
            size="icon"
            onClick={() => {
                const newTheme = resolvedTheme === "dark" ? "light" : "dark"
                setTheme(newTheme)
            }}
            className="relative h-10 w-10 hover:brightness-125"
        >
            <Sun className="h-[1.5rem] w-[1.5rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-[1.5rem] w-[1.5rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Toggle theme</span>
        </Button>
    )
} 