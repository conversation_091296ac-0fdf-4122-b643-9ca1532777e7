"use client"

import { createContext, useContext, useState, useEffect } from 'react'

type SidebarContextType = {
    isCollapsed: boolean
    setIsCollapsed: (value: boolean) => void
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined)

export function SidebarProvider({ children }: { children: React.ReactNode }) {
    const [isCollapsed, setIsCollapsed] = useState(false)

    useEffect(() => {
        // Load initial state from localStorage
        const savedState = localStorage.getItem('sidebarCollapsed')
        if (savedState !== null) {
            setIsCollapsed(JSON.parse(savedState))
        }
    }, [])

    const updateCollapsed = (value: boolean) => {
        setIsCollapsed(value)
        localStorage.setItem('sidebarCollapsed', JSON.stringify(value))
    }

    return (
        <SidebarContext.Provider value={{ isCollapsed, setIsCollapsed: updateCollapsed }}>
            {children}
        </SidebarContext.Provider>
    )
}

export function useSidebar() {
    const context = useContext(SidebarContext)
    if (context === undefined) {
        throw new Error('useSidebar must be used within a SidebarProvider')
    }
    return context
} 