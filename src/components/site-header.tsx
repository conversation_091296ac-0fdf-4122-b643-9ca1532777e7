"use client"

import { Sheet, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>u, ChevronLeft, ChevronRight } from "lucide-react"
import {
    HomeIcon,
    MagnifyingGlassIcon,
    DevicePhoneMobileIcon,
    Squares2X2Icon,
    Square3Stack3DIcon,
    DocumentTextIcon,
    BookOpenIcon,
} from '@heroicons/react/24/outline'
import Image from "next/image"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSession } from "next-auth/react"
import Avatar from '@/app/components/avatar'
import { ThemeToggle } from "./theme-toggle"
import { useSidebar } from "./sidebar-context"

// Navigation types
interface NavigationItem {
    name: string;
    href: string;
    icon: React.ComponentType<any>;
    current: boolean;
}

interface TeamItem extends NavigationItem {
    id: number;
    initial: string;
}

function classNames(...classes: any[]) {
    return classes.filter(Boolean).join(' ')
}

export function SiteHeader() {
    const pathname = usePathname()
    const { data: session } = useSession()
    const { isCollapsed, setIsCollapsed } = useSidebar()

    const toggleCollapsed = () => {
        setIsCollapsed(!isCollapsed)
    }

    const navigation: NavigationItem[] = [
        { name: '首頁', href: '/home', icon: HomeIcon, current: pathname == '/home' },
        { name: '全部漫畫', href: '/books?continued=true', icon: Squares2X2Icon, current: pathname == '/books' },
        { name: '搜索', href: '/search', icon: MagnifyingGlassIcon, current: pathname == '/search' },
        { name: '下載APP', href: '/app-download', icon: DevicePhoneMobileIcon, current: pathname == '/app-download' },
        { name: '福利', href: '/games', icon: Square3Stack3DIcon, current: pathname == '/games' },
    ]

    const teams: TeamItem[] = [
        { id: 1, name: '我的書架', href: '/my-book-shelf?tab=reading', icon: BookOpenIcon, initial: 'S', current: pathname == '/my-book-shelf' },
        { id: 2, name: '閱讀歷史', href: '/read-history', initial: 'H', icon: DocumentTextIcon, current: pathname == '/read-history' },
    ]

    const NavContent = ({ onClose }: { onClose?: () => void }) => (
        <div className={classNames(
            "flex flex-col h-full bg-background border-r transition-all duration-300",
            isCollapsed ? "lg:w-20" : "lg:w-72"
        )}>
            <div className="flex items-center h-16 px-6">
                {!isCollapsed && (
                    <>
                        <a href="/home" className="flex items-center">
                            <Image
                                src="/android-chrome-192x192.png"
                                alt="rouman"
                                className="h-10 w-auto"
                                width={60}
                                height={60}
                                priority
                            />
                            <div className="text-2xl font-semibold ml-2 text-foreground">
                                <span className="text-primary">肉</span>漫屋
                            </div>
                        </a>
                    </>
                )}
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggleCollapsed}
                    className="ml-auto hidden lg:flex"
                >
                    {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
                </Button>
            </div>

            <nav className="flex-1 px-6">
                <div className="space-y-4">
                    <div>
                        <div className="mt-2 space-y-1">
                            {navigation.map((item) => (
                                <a
                                    key={item.name}
                                    href={item.href}
                                    onClick={onClose}
                                    className={classNames(
                                        item.current ? 'bg-muted text-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-muted',
                                        'group flex items-center gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold',
                                        isCollapsed && 'lg:justify-center lg:px-2'
                                    )}
                                    title={isCollapsed ? item.name : undefined}
                                >
                                    <item.icon className="h-6 w-6 shrink-0" aria-hidden="true" />
                                    {!isCollapsed && item.name}
                                </a>
                            ))}
                        </div>
                    </div>
                    {isCollapsed && <div className="my-2 border-t border-border mx-2" />}
                    <div>
                        <div className={classNames(
                            "text-xs font-semibold leading-6 text-muted-foreground",
                            isCollapsed && "lg:text-center"
                        )}>
                            {!isCollapsed && "會員功能"}
                        </div>
                        <div className="mt-2 space-y-1">
                            {teams.map((team) => (
                                <a
                                    key={team.name}
                                    href={team.href}
                                    onClick={onClose}
                                    className={classNames(
                                        team.current ? 'bg-muted text-foreground' : 'text-muted-foreground hover:text-foreground hover:bg-muted',
                                        'group flex items-center gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold',
                                        isCollapsed && 'lg:justify-center lg:px-2'
                                    )}
                                    title={isCollapsed ? team.name : undefined}
                                >
                                    <team.icon className="h-6 w-6 shrink-0" aria-hidden="true" />
                                    {!isCollapsed && team.name}
                                </a>
                            ))}
                        </div>
                    </div>
                </div>
            </nav>

            <div className={classNames(
                "px-6 py-4 flex items-center gap-4",
                isCollapsed ? "lg:justify-center lg:px-2" : "lg:flex",
                "hidden"
            )}>
                <ThemeToggle />
                {session && <Avatar email={session.user?.email!} />}
                {!session && !isCollapsed && (
                    <Link href="/auth/email-signin" onClick={onClose} className="w-full">
                        <Button className="w-full bg-primary hover:bg-primary/90">登入</Button>
                    </Link>
                )}
            </div>
        </div>
    )

    return (
        <>
            {/* Mobile */}
            <div className="lg:hidden sticky top-0 z-[4000] flex items-center gap-x-6 bg-background border-b px-4 py-2 shadow-sm sm:px-6">
                <Sheet>
                    <SheetTrigger asChild>
                        <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
                            <Menu className="h-5 w-5" />
                        </Button>
                    </SheetTrigger>
                    <SheetContent side="left" className="p-0 w-72 z-[5000]">
                        <NavContent />
                    </SheetContent>
                </Sheet>

                <div className="flex-1 text-2xl font-semibold leading-6 text-foreground">
                    <a href="/home">
                        <span className="text-primary">肉</span>漫屋
                    </a>
                </div>

                <div className="flex items-center gap-4">
                    <ThemeToggle />
                    {session && <Avatar email={session.user?.email!} />}
                    {!session && (
                        <Link href="/auth/email-signin">
                            <Button className="bg-primary hover:bg-primary/90">登入</Button>
                        </Link>
                    )}
                </div>
            </div>

            {/* Desktop */}
            <div className={classNames(
                "hidden lg:fixed lg:inset-y-0 lg:z-[3100] lg:flex lg:flex-col",
                isCollapsed ? "lg:w-20" : "lg:w-72",
                "transition-all duration-300"
            )}>
                <NavContent />
            </div>
        </>
    )
} 