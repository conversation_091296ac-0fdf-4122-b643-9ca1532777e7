import NextAuth, { type DefaultSession } from "next-auth"
import 'next-auth/jwt';
import { PrismaAdapter } from "@auth/prisma-adapter"
import Keycloak from "next-auth/providers/keycloak"
import prisma from '@/app/lib/prisma';

declare module "next-auth" {
  /**
   * Returned by `auth`, `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      /** The user's postal address. */
      roles?: string[]
      /**
       * By default, TypeScript merges new interface properties and overwrites existing ones.
       * In this case, the default session user properties will be overwritten,
       * with the new ones defined above. To keep the default session user properties,
       * you need to add them back into the newly declared interface.
       */
    } & DefaultSession["user"]
  }

  interface User {
    // Custom fields
    roles?: string[]
  }

  interface Profile {
    realm_access?: {
      roles?: string[]
    }
  }
}

declare module "next-auth/jwt" {
  /** Returned by the `jwt` callback and `auth`, when using JWT sessions */
  interface JWT {
    roles?: string[]
  }
}

export const { handlers, signIn, signOut, auth } = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [Keycloak({ allowDangerousEmailAccountLinking: true })],
  trustHost: true,
  session: {
    strategy: "jwt",
  },
  callbacks: {
    jwt({ token, user, account, profile }) {
      // console.log("jwt", token, user, account, profile);
      if (profile?.realm_access && profile.realm_access?.roles) {
        token.roles = profile.realm_access.roles;
      }
      return token;
    },
    session({ session, token, user }) {
      // console.log("session", session, token, user);
      return {
        ...session,
        user: {
          ...session.user,
          id: token.sub,
          roles: token.roles,
        },
      }
    },
  },
})