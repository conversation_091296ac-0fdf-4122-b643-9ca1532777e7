import { BookOpenIcon, MagnifyingGlassIcon } from '@heroicons/react/20/solid'
import React from "react";


export default async function SearchBox({ term }: { term: string }) {

  return (
    <form>
      <div className="mt-2 flex rounded-md shadow-sm">
        <div className="relative flex flex-grow items-stretch focus-within:z-10">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <BookOpenIcon aria-hidden="true" className="h-5 w-5 text-gray-400" />
          </div>
          <input
            id="term"
            name="term"
            placeholder="搜索漫畫，作者..."
            className="block w-full rounded-none rounded-l-md border-0 py-1.5 pl-10 text-foreground ring-1 ring-inset ring-rose-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            defaultValue={term}
          />
        </div>
        <button
          type="submit"
          className="relative -ml-px inline-flex items-center gap-x-1.5 rounded-r-md px-3 py-2 text-sm font-semibold text-gray-100 ring-1 ring-inset ring-rose-300 bg-rose-600 hover:bg-rose-400"
        >
          <MagnifyingGlassIcon aria-hidden="true" className="-ml-0.5 h-5 w-5 text-gray-100" />
          搜索
        </button>
      </div>

    </form>
  )
}