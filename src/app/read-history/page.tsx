import prisma from '@/app/lib/prisma';
import { auth } from "@/auth";
import ComicList from "@/app/components/comicList";
import Pagination from '@/app/components/pagination';
import RequireSignIn from "@/app/auth/email-signin/page";
import { DocumentTextIcon } from '@heroicons/react/24/outline';
import AdsBanner from "@/app/components/AdsBanner";

const itemPerPage = 15;

async function getHistory(userId: string, page: number) {
  if (page < 0) {
    page = 0;
  }

  const totalNum = await prisma.readHistory.count({ where: { userId: userId } });
  const total = Math.ceil(Number(totalNum) / itemPerPage);

  const skipNum = page * itemPerPage;
  const books = await prisma.readHistory.findMany({
    where: { userId: userId },
    include: { book: { include: { activeResource: { include: { chapters: { orderBy: { ind: "asc" } } } } } }, chapter: true },
    orderBy: [{ createdAt: "desc" }],
    take: itemPerPage,
    skip: skipNum,
  });


  return { books, total };
}

export default async function ReadHistory({ searchParams }: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  const session = await auth();
  if (!session) return (
    <RequireSignIn />
  )
  const userId = session?.user?.id;
  console.log("user id:", userId);

  if (!userId) {
    return (
      <RequireSignIn />
    )
  }

  console.log("page:", searchParams.page);
  const pageNum = searchParams.page ? Number(searchParams.page) : 0;

  const { books, total } = await getHistory(userId, pageNum);

  return (
    <div className='px-1'>
      <div className='flex items-center space-x-1 text-xl my-2 sm:my-4 text-gray-600 dark:text-gray-300'>
        <DocumentTextIcon className="h-7 w-7 shrink-0" />
        <div>閱讀歷史</div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 my-2">
        <div className='flex justify-center'>
          <AdsBanner id="ckunfc3nm01420hj9kqjfo2ea" />
        </div>
        <div className='flex justify-center'>
          <AdsBanner id="ckv6gba8c24350im3gr9wwg45" />
        </div>
      </div>

      {books.map((rh: any) => {
        const chapter = rh.chapter;
        const chapters = rh.book.activeResource?.chapters;
        const lastChapter = chapters.at(-1);
        return (
          <ComicList key={rh.bookId} book={rh.book} lastRead={chapter} lastChapter={lastChapter} showDate={rh.createdAt} shortDate={false} />
        )
      })}

      <div className='my-8'>
        <Pagination path='read-history?tag=' current={pageNum} total={total} />
      </div>

    </div>
  )
}