import { DevicePhoneMobileIcon } from '@heroicons/react/24/outline'
import Image from 'next/image';
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

const UpgradeServerBase = "https://rou.pub";

async function getDownloadUrl() {
  const json = await fetch(`${UpgradeServerBase}/latest-android.json`, {
    headers: {
      'Cache-Control': 'no-cache'
    }
  }).then((res) => res.json());

  return json;
}

export default async function AppDownload() {
  const appInfo = await getDownloadUrl();

  return (
    <div className='container mx-auto px-4 py-4'>
      <div className='flex items-center space-x-2 text-xl mb-6 text-foreground'>
        <DevicePhoneMobileIcon className="h-7 w-7 shrink-0" />
        <div className="font-medium">下載手機APP</div>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardContent className="pt-6">
          <div className='space-y-6'>
            <div className='text-muted-foreground'>
              下載手機App，突破任何封鎖線！持續品嚐肉的味道！
            </div>

            <div className='grid grid-cols-2 gap-4 justify-center'>
              <Image
                src="/android.png"
                width={300}
                height={600}
                alt='Android App screenshot'
                className="rounded-lg shadow-lg border"
              />
              <Image
                src="/ios.png"
                width={300}
                height={600}
                alt='iOS App screenshot'
                className="rounded-lg shadow-lg border"
              />
            </div>

            <div className='flex flex-col sm:flex-row gap-4'>
              <a
                href={appInfo.downloadUrl}
                className="inline-flex items-center justify-center gap-x-2 rounded-md bg-[#3DDC84] px-4 py-3 text-base font-semibold text-white shadow transition-colors hover:bg-[#3DDC84]/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[#3DDC84]"
              >
                <svg height="40" viewBox="32.9 57.77898118 414.2 366.56939652" width="50" xmlns="http://www.w3.org/2000/svg" className="text-white">
                  <path fill="currentColor" d="m64.2 370.885c-10.7 0-17.9 8.4-17.9 19.2s7.2 19.2 17.9 19.2 17.9-8.4 17.9-19.2c-.1-10.8-7.3-19.2-17.9-19.2m-2.4-11.9c8.6 0 15.8 4.4 19.2 9.2v-8h13.2v59.9h-10.2a3.009 3.009 0 0 1 -3-3v-5c-3.4 4.8-10.5 9.2-19.2 9.2-16.4 0-28.9-14-28.9-31.1s12.4-31.2 28.9-31.2m47.4 1.2h13.2v8.1c3.6-6 10.4-9.3 18.2-9.3 13.9 0 22.9 9.8 22.9 25v36.1h-10.1a3.009 3.009 0 0 1 -3-3v-30.9c0-9.5-4.8-15.2-12.5-15.2-8.7 0-15.5 6.8-15.5 19.6v29.5h-10.1a3.009 3.009 0 0 1 -3-3zm95.9 10.7c-10.7 0-17.9 8.4-17.9 19.2s7.2 19.2 17.9 19.2 17.9-8.4 17.9-19.2-7.2-19.2-17.9-19.2m-2.4-11.9c8.6 0 15.8 4.4 19.2 9.2v-38h13.2v89.9h-10.1a3.009 3.009 0 0 1 -3-3v-5c-3.4 4.8-10.5 9.2-19.2 9.2-16.4 0-28.9-14-28.9-31.1s12.4-31.2 28.8-31.2m47.4 1.2h13.2v10.7a16.961 16.961 0 0 1 15.8-11.3 22.063 22.063 0 0 1 5.1.5v13.5a21.243 21.243 0 0 0 -6.6-1.1c-7.5 0-14.4 6.4-14.4 18.3v29.2h-10.1a3.009 3.009 0 0 1 -3-3zm69.6 48.8c10.5 0 18-8.3 18-18.9 0-10.7-7.4-18.9-18-18.9-10.7 0-18.1 8.3-18.1 18.9.1 10.7 7.4 18.9 18.1 18.9m0 12.3a31.151 31.151 0 1 1 31.4-31.1 30.948 30.948 0 0 1 -31.4 31.1m42.1-61.1h13.2v59.9h-10.1a3.009 3.009 0 0 1 -3-3zm6.6-13.1a8.985 8.985 0 0 1 -8.9-8.9 8.88 8.88 0 0 1 8.9-8.7 8.8 8.8 0 0 1 0 17.6m48.7 23.8c-10.7 0-17.9 8.4-17.9 19.2s7.2 19.2 17.9 19.2 17.9-8.4 17.9-19.2c-.2-10.8-7.2-19.2-17.9-19.2m-2.4-11.9c8.6 0 15.8 4.4 19.2 9.2v-38h13.2v89.9h-10.1a3.009 3.009 0 0 1 -3-3v-5c-3.4 4.8-10.5 9.2-19.2 9.2-16.4 0-28.9-14-28.9-31.1s12.4-31.2 28.8-31.2" />
                  <path d="m341.5 129.185 34.5-59.7a7.17 7.17 0 0 0 -12.4-7.2l-34.9 60.4a216.078 216.078 0 0 0 -177.2 0l-34.9-60.4a7.17 7.17 0 0 0 -12.4 7.2l34.5 59.7c-59.5 32.2-99.9 92.1-105.8 162.8h414c-5.9-70.7-46.4-130.6-105.4-162.8zm-196.8 103.8a17.2 17.2 0 1 1 17.2-17.2 17.2 17.2 0 0 1 -17.2 17.2zm190.6 0a17.2 17.2 0 1 1 17.2-17.2 17.2 17.2 0 0 1 -17.2 17.2z" fill="currentColor" />
                </svg>
                下載Android APK安裝包
              </a>

              <a
                href={appInfo.iosDownloadUrl}
                className="inline-flex items-center justify-center gap-x-2 rounded-md bg-black px-4 py-3 text-base font-semibold text-white shadow transition-colors hover:bg-black/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-black"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="50" height="40" viewBox="0.276 0.525 124.326 79.116" className="text-destructive-foreground">
                  <path fill="currentColor" d="M4.621 6.965c0 1.368-.833 2.38-2.262 2.38-1.19 0-2.083-1.012-2.083-2.38 0-1.31.952-2.381 2.202-2.381 1.31 0 2.143 1.071 2.143 2.381zM1.327 78.5V23.78H3.57V78.5H1.327zM78.174 39.512c0 27.588-15.315 40.129-32.079 40.129-17.125 0-30.993-13.565-30.993-38.988C15.102 14.549 29.453.526 47.301.526 64.787.524 78.174 14.318 78.174 39.512zm-60.799.57C17.375 61 27.513 77.25 46.201 77.25c18.818 0 29.611-16.75 29.611-37.406 0-19.155-8.648-36.636-28.382-36.636S17.375 19.667 17.375 40.082zM86.406 72.571c3.763 2.508 10.258 4.93 15.844 4.93 10.602 0 20.031-7.417 20.031-18.334 0-10.131-6.281-15.417-16.945-19.958-9.544-4.064-18.125-8.475-18.125-19.305 0-11.285 8.891-19.267 20.975-19.267 6.498 0 11.4 1.824 13.68 3.42l-.906 1.968c-1.938-1.367-7.176-3.109-12.874-3.109-12.771 0-18.334 9.65-18.334 16.754 0 9.812 7.606 13.093 17.41 17.767 11.399 5.585 17.44 10.51 17.44 21.227 0 11.514-8.207 20.86-22.799 20.86-6.043 0-12.996-2.051-16.416-4.674l1.019-2.279z" />
                </svg>
                下載iOS IPA安裝包
              </a>
            </div>

            <div className='text-sm text-muted-foreground'>發佈版本 {appInfo.versionName}</div>
            <div className='space-y-2'>
              <a
                href='https://docs.rouman5.org'
                target='_blank'
                className='text-primary hover:text-primary/90 text-lg font-medium underline underline-offset-4'
              >
                iOS版APP安裝手冊
              </a>

              <div className='text-sm text-muted-foreground italic'>有些安卓手機會自動修改下載文件的擴展名為.zip，請手動修改為.apk</div>
              <div className='text-sm text-muted-foreground italic'>
                如無法下載請拷貝下載鏈接，在新窗口粘貼打開
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}