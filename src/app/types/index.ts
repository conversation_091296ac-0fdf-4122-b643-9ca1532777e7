import type { Book, BookResource, Chapter } from '@prisma/client';

// Book related types
export type BookWithResource = Book & {
    activeResource: (BookResource & {
        chapters: Chapter[];
    }) | null;
};

export interface HotBooksResponse {
    aggregations?: {
        hotBook?: {
            buckets?: Array<{
                key: string;
                doc_count: number;
            }>;
        };
    };
}

// Search types
export interface SearchParams {
    page: number;
    limit: number;
    tag?: string;
    continued?: boolean;
    sort?: string;
}

// Shelf types
export interface BookShelfData {
    readingBooks: any[];
    completedBooks: any[];
    discontinuedCompletedBooks: any[];
}
