'use client'

import React from "react";
import Image from "next/image";
import { PlayCircleIcon } from "@heroicons/react/24/outline";
import { Card } from "@/components/ui/card";

export default function Games() {
  const [vApps, setVApps] = React.useState<any[]>();
  const [mgGame, setMgGame] = React.useState<any[]>();
  const [jsGame, setJsGame] = React.useState<any[]>();
  const [jg, setJg] = React.useState<any[]>();
  const [hGame, setHGame] = React.useState<any[]>();

  React.useEffect(() => {
    // 視頻app
    fetch("https://ra12.xyz/z/clfd9t48j0003ks0i832vsryy/json")
      .then(resp => resp.json())
      .then(json => {
        const ads = json.sort((x: any, y: any) => x.order > y.order ? 1 : -1);
        setVApps(ads);
      })

    // 木瓜 game
    fetch("https://ra12.xyz/z/cmf3l7xmi001kb601bqpxtq5a/json")
      .then(resp => resp.json())
      .then(json => {
        const ads = json.sort((x: any, y: any) => x.order > y.order ? 1 : -1);
        setMgGame(ads);
      })

    // js game
    fetch("https://ra12.xyz/z/cli425se60007ng0ik7i7k4q7/json")
      .then(resp => resp.json())
      .then(json => {
        const ads = json.sort((x: any, y: any) => x.order > y.order ? 1 : -1);
        setJsGame(ads);
      })

    // jg game
    fetch("https://ra12.xyz/z/clf98c0mn000hlt0i5jj1nko4/json")
      .then(resp => resp.json())
      .then(json => {
        const ads = json.sort((x: any, y: any) => x.order > y.order ? 1 : -1);
        setJg(ads);
      })

    // h game
    fetch("https://ra12.xyz/z/cmd2yw12p0001de0156pvr34r/json")
      .then(resp => resp.json())
      .then(json => {
        const ads = json.sort((x: any, y: any) => x.order > y.order ? 1 : -1);
        setHGame(ads);
      })

  }, [])

  return (
    <div className='container mx-auto px-4 py-6'>
      <div className='flex items-center space-x-2 text-xl mb-8 text-foreground'>
        <PlayCircleIcon className="h-7 w-7 shrink-0" />
        <div className="font-medium">福利</div>
      </div>

      <div className="space-y-8">
        <section>
          <h2 className="text-xl font-medium text-foreground mb-4">在線視頻</h2>
          <div className='grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 gap-6'>
            {vApps && vApps.map(ad => (
              <Card key={ad.name} className="group overflow-hidden border-0 shadow-lg">
                <a href={ad.link_url} target="_blank" className="block transition-transform hover:scale-105">
                  <Image
                    src={ad.image_url}
                    width={300}
                    height={300}
                    alt={ad.name}
                    className="w-full h-auto object-contain"
                  />
                  <div className="p-3">
                    <div className="text-sm text-center text-foreground font-medium">{ad.name}</div>
                  </div>
                </a>
              </Card>
            ))}
          </div>
        </section>

        <section>
          <h2 className="text-xl font-medium text-foreground mb-4">遊戲: 木瓜玩出品</h2>
          <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6'>
            {mgGame && mgGame.map(ad => (
              <Card key={ad.name} className="group overflow-hidden border-0 shadow-lg">
                <a href={ad.link_url} target="_blank" className="block transition-transform hover:scale-105">
                  <Image
                    src={ad.image_url}
                    width={600}
                    height={500}
                    alt={ad.name}
                    className="w-full h-auto object-contain"
                    unoptimized
                  />
                  <div className="p-3">
                    <div className="text-sm text-center text-foreground font-medium">{ad.name}</div>
                  </div>
                </a>
              </Card>
            ))}
          </div>
        </section>

        <section>
          <h2 className="text-xl font-medium text-foreground mb-4">遊戲: JS Game 出品</h2>
          <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6'>
            {jsGame && jsGame.map(ad => (
              <Card key={ad.name} className="group overflow-hidden border-0 shadow-lg">
                <a href={ad.link_url} target="_blank" className="block transition-transform hover:scale-105">
                  <Image
                    src={ad.image_url}
                    width={600}
                    height={500}
                    alt={ad.name}
                    className="w-full h-auto object-contain"
                    unoptimized
                  />
                  <div className="p-3">
                    <div className="text-sm text-center text-foreground font-medium">{ad.name}</div>
                  </div>
                </a>
              </Card>
            ))}
          </div>
        </section>

        <section>
          <h2 className="text-xl font-medium text-foreground mb-4">遊戲: H Game 出品</h2>
          <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6'>
            {hGame && hGame.map(ad => (
              <Card key={ad.name} className="group overflow-hidden border-0 shadow-lg">
                <a href={ad.link_url} target="_blank" className="block transition-transform hover:scale-105">
                  <Image
                    src={ad.image_url}
                    width={600}
                    height={500}
                    alt={ad.name}
                    className="w-full h-auto object-contain"
                    unoptimized
                  />
                  <div className="p-3">
                    <div className="text-sm text-center text-foreground font-medium">{ad.name}</div>
                  </div>
                </a>
              </Card>
            ))}
          </div>
        </section>

        <section>
          <h2 className="text-xl font-medium text-foreground mb-4">遊戲: JG Game 出品</h2>
          <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-6'>
            {jg && jg.map(ad => (
              <Card key={ad.name} className="group overflow-hidden border-0 shadow-lg">
                <a href={ad.link_url} target="_blank" className="block transition-transform hover:scale-105">
                  <Image
                    src={ad.image_url}
                    width={600}
                    height={500}
                    alt={ad.name}
                    className="w-full h-auto object-contain"
                    unoptimized
                  />
                  <div className="p-3">
                    <div className="text-sm text-center text-foreground font-medium">{ad.name}</div>
                  </div>
                </a>
              </Card>
            ))}
          </div>
        </section>

      </div>
    </div>
  )
}