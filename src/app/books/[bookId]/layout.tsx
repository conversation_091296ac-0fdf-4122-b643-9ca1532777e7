
import type { Metadata, ResolvingMetadata } from 'next';

import { getBook } from '@/app/lib/pageUtil';
import { genCoverImageUrl } from "@/app/lib/imageUrl";


type Props = {
  params: { bookId: string }
}

export const revalidate = 300 // revalidate the data at most 5 mins

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  // fetch data
  const { bookId } = await params;
  const book = await getBook(bookId);

  // optionally access and extend (rather than replace) parent metadata
  // const previousImages = (await parent).openGraph?.images || []

  const bookName = book?.name;
  const alias = book?.alias.join(",")
  let bookTitle = bookName ? `《${bookName}》別名:${alias}` : undefined;

  const imageUrl = genCoverImageUrl(bookId);
  // if (params.ind) {
  //   const ind = Number(params.ind);
  //   const chapter = book?.activeResource?.chapters.at(ind);
  //   bookTitle = `${bookTitle}-${chapter?.name}`;
  // }

  return {
    title: `${bookTitle} | 漫畫免費在線觀看-肉漫屋`,
    description: book?.description,
    openGraph: {
      images: [imageUrl],
      title: `${bookTitle} | 漫畫免費在線觀看-肉漫屋`,
    },
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>{children}</>
  );
}
