import { ArrowLongLeftIcon, ArrowLongRightIcon } from '@heroicons/react/20/solid'

export default function Pagination({ book_id, current, total }: { book_id: string, current: number, total: number }) {
  const pre = current == 0 ? 0 : current - 1;
  const next = current >= total - 1 ? current : current + 1;
  return (
    <div className="flex justify-between">
      <a href={`/books/${book_id}/${pre}`}>
        <button
          type="button"
          className="inline-flex items-center gap-x-1.5 rounded-md bg-primary px-3 py-3 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary disabled:bg-muted"
          disabled={current === 0}
        >
          <ArrowLongLeftIcon aria-hidden="true" className="-ml-0.5 h-5 w-5" />
          上一頁
        </button>
      </a>
      <a href={`/books/${book_id}`}>
        <button
          type="button"
          className="inline-flex items-center gap-x-1.5 rounded-md bg-primary px-3 py-3 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"
        >
          目錄
        </button>
      </a>
      <a href={`/books/${book_id}/${next}`}>
        <button
          type="button"
          className="inline-flex items-center gap-x-1.5 rounded-md bg-primary px-3 py-3 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary disabled:bg-muted"
          disabled={current >= total - 1}
        >
          下一頁
          <ArrowLongRightIcon aria-hidden="true" className="-mr-0.5 h-5 w-5" />
        </button>
      </a>
    </div>
  )
}