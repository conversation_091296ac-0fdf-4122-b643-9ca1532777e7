import { ArrowLongLeftIcon, ArrowLongRightIcon } from '@heroicons/react/20/solid'

export default function Pagination({ book_id, current, total }: { book_id: string, current: number, total: number }) {
  const pre = current == 0 ? 0 : current - 1;
  const next = current >= total - 1 ? current : current + 1;
  return (
    <div className="flex justify-between">
      <a href={`/books/${book_id}/${pre}/edit`}>
        <button
          type="button"
          className="inline-flex items-center gap-x-1.5 rounded-md bg-rose-500 px-3 py-3 text-sm font-semibold text-white shadow-sm hover:bg-rose-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-rose-600 disabled:bg-gray-300"
          disabled={current === 0}
        >
          <ArrowLongLeftIcon aria-hidden="true" className="-ml-0.5 h-5 w-5" />
          上一頁
        </button>
      </a>
      <a href={`/books/${book_id}`}>
        <button
          type="button"
          className="inline-flex items-center gap-x-1.5 rounded-md bg-rose-500 px-3 py-3 text-sm font-semibold text-white shadow-sm hover:bg-rose-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-rose-600"
        >
          目錄
        </button>
      </a>
      <a href={`/books/${book_id}/${next}/edit`}>
        <button
          type="button"
          className="inline-flex items-center gap-x-1.5 rounded-md bg-rose-500 px-3 py-3 text-sm font-semibold text-white shadow-sm hover:bg-rose-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-rose-600 disabled:bg-gray-300"
          disabled={current >= total - 1}
        >
          下一頁
          <ArrowLongRightIcon aria-hidden="true" className="-mr-0.5 h-5 w-5" />
        </button>
      </a>
    </div>
  )
}