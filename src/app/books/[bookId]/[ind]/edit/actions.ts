'use server'

import { getBook, isAdmin } from "@/app/lib/pageUtil";
import { genImageUrls } from "@/app/lib/imageUrl";
import { auth } from "@/auth";
import prisma from '@/app/lib/prisma';


export async function getImages(bookId: string, ind: string) {
  const session = await auth();
  if (!isAdmin(session)) throw "Unauthorized";

  const book = await getBook(bookId);
  const activeResource = book?.activeResource;
  if (!book || !activeResource) {
    throw "no book or resource";
  }
  // const chapters = book?.activeResource?.chapters.filter(c => c.ind.toString() === params.ind);
  const chapters = book?.activeResource?.chapters;
  if (!chapters) {
    throw "no chapters";
  }

  const inds = chapters.map((c: { ind: number }) => c.ind);
  // console.log("inds:", inds);

  const current = Number(ind);
  const chapterInd = inds.at(current);
  // const total = chapters?.at(-1)?.ind!;
  const total = inds.length;
  // console.log("current;", current, "chapterInd:", chapterInd, "total:", total);
  const chapter = chapters?.find(chapter => chapter.ind == chapterInd);
  if (!chapter) {
    throw "no chapter";
  }
  // console.log("chapter:", chapter);

  const imageUrls = await genImageUrls(book?.id, activeResource?.resourceKey, chapter?.imagePaths);


  return { imagePaths: chapter?.imagePaths, imageUrls: imageUrls, chapter: chapter, inds };
}

export async function updateImages(chapterId: string, imagePaths: string[]) {
  const session = await auth();
  if (!isAdmin(session)) throw "Unauthorized";

  // console.log("imagePaths:", imagePaths);

  await prisma.chapter.update({
    where: {
      id: chapterId
    },
    data: {
      imagePaths: imagePaths
    }
  });

  return { message: "success" };
}