'use client'

import Viewport from "@/app/books/[bookId]/[ind]/viewport";
import React from "react";

import Pagination from './pagination';
import { getImages, updateImages } from './actions';

export default function ChapterEditPage({ params }: { params: { bookId: string, ind: string } }) {
  const [imagePaths, setImagePaths] = React.useState<string[]>([]);
  const [originalImagePaths, setOriginalImagePaths] = React.useState<string[]>([]);
  const [imageUrls, setImageUrls] = React.useState<string[]>([]);
  const [checkedImages, setCheckedImages] = React.useState<string[]>([]);
  const [chapter, setChapter] = React.useState<any>(null);
  const [inds, setInds] = React.useState<number[]>([]);

  React.useEffect(() => {
    const fetchData = async () => {
      const { imagePaths: paths, imageUrls, chapter, inds } = await getImages(params.bookId, params.ind);
      if (imageUrls) {
        setImagePaths(paths);
        setOriginalImagePaths(paths);
        setImageUrls(imageUrls);
        setCheckedImages(imageUrls); // Initialize checkedImages with all imageUrls
        setChapter(chapter);
        setInds(inds);
      }
    };
    fetchData();
  }, [params.bookId, params.ind]);

  const handleCheckboxChange = (imageUrl: string, checked: boolean) => {
    const index = imageUrls.indexOf(imageUrl);
    if (index !== -1) {
      const imagePath = originalImagePaths[index]; // retrieve the correct imagePath
      if (checked) {
        setImagePaths((prevImagePaths) => {
          const newPath = [...prevImagePaths];
          newPath.splice(index, 0, imagePath);
          return newPath;
        });
      } else {
        setImagePaths((prevImagePaths) => prevImagePaths.filter((path) => path !== imagePath));
      }
    }
    setCheckedImages((prevCheckedImages) => checked ? [...prevCheckedImages, imageUrl] : prevCheckedImages.filter((image) => image !== imageUrl));
  };

  const handleUpdateImages = async () => {
    // Send the imagePaths to the server-side action to update the actual list of images
    // You can call your server-side action here
    // console.log("imagePaths:", imagePaths);
    updateImages(chapter.id, imagePaths);
  };

  return (
    <div>
      <div className="flex justify-center bg-gray-200" >{chapter?.name}</div>
      <div className="my-2">
        <div className='text-gray-400 text-right mr-4'>{Number(params.ind) + 1}/{inds.length}頁</div>
        <Pagination book_id={params.bookId} current={Number(params.ind)} total={inds.length} />
      </div>
      <div className="text-center my-2">
        <button onClick={handleUpdateImages} className="bg-blue-600 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded">更新圖片列表</button>
      </div>
      <div className="max-w-full flex flex-wrap">
        {imageUrls.map((ip, ind) => (
          <div className="flex justify-center w-60" key={ind}>
            <div className="text-center">
              <div>{ind}</div>
              <input
                type="checkbox"
                checked={checkedImages.includes(ip)}
                onChange={(e) => handleCheckboxChange(ip, e.target.checked)}
              />
              <Viewport imageUrl={ip} ind={ind} />
            </div>
          </div>
        ))}
      </div>
      <div className="text-center my-2">
        <button onClick={handleUpdateImages} className="bg-blue-600 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded">更新圖片列表</button>
      </div>
      <div className="my-2">
        <Pagination book_id={params.bookId} current={Number(params.ind)} total={inds.length} />
      </div>
    </div>
  );
}