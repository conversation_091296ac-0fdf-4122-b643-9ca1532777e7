"use client"

import React from "react";
import handleViewport, { type InjectedViewportProps } from 'react-in-viewport';
const md5 = require("md5");

export interface Props extends InjectedViewportProps<HTMLDivElement> {
  imageUrl: string;
  ind: number;
}

const Block = (props: Props) => {
  const { inViewport, forwardedRef, imageUrl, ind } = props;
  const scramble = imageUrl.includes("sr:1");
  if (inViewport) {
    const fimg = document.getElementById(`image_${ind}`) as HTMLImageElement;
    if (fimg) {
      fimg.setAttribute("src", imageUrl);

      if (scramble) {
        // let canvas = inputEl.current;
        let canvas = forwardedRef.current!.getElementsByTagName("canvas")[0];
        if (!canvas) {
          canvas = document.createElement("canvas");
          forwardedRef.current!.appendChild(canvas);
          canvas.style.width = "100%";
          canvas.style.zIndex = "10";
          canvas.style.left = "0";
          canvas.style.top = "0";
          canvas.style.position = "absolute";
          // forwardedRef.current!.style.height = "auto";
        }

        const ctx = canvas.getContext("2d", { alpha: false })!;
        ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
        // var img = document.createElement("img");
        fimg.onload = function () {
          // var s_w = img.width; //顯示尺寸
          var w = fimg.naturalWidth; //原始尺寸
          var h = fimg.naturalHeight;
          canvas.width = w;
          canvas.height = h;

          const segs = imageUrl.split("/");
          const imageFilePath = segs[segs.length - 1];
          const imageFilePathNoExt = imageFilePath.split('.').slice(0, -1).join('.');
          // console.log("imageFilePath", imageFilePath);
          // console.log("imageFilePath2", atob(imageFilePath));
          const digest = md5(atob(imageFilePathNoExt));
          // console.log("digest", digest);
          const buf = Buffer.from(digest, "hex");
          const lastE = buf.slice(-1)[0];
          // console.log("num", (lastE % 10) + 5);
          var num = (lastE % 10) + 5;
          var remainder = h % num;
          var copyW = w;
          for (var i = 0; i < num; i++) {
            var copyH = Math.floor(h / num);
            var py = copyH * i;
            var y = h - copyH * (i + 1) - remainder;

            if (i == 0) {
              copyH = copyH + remainder;
            } else {
              py = py + remainder;
            }
            ctx.drawImage(fimg, 0, y, copyW, copyH, 0, py, copyW, copyH);
          }
        };
        // img.setAttribute("referrerPolicy", "no-referrer");
        // img.setAttribute("src", imageUrl);
      }
    }

  } else {
    // not in view
    if (scramble) {
      if (forwardedRef.current) {
        let canvas = forwardedRef.current!.getElementsByTagName("canvas")[0];
        if (canvas) {
          canvas.remove();
        }
      }
    }
  }

  return (
    <div ref={forwardedRef} className="position: relative">
      {/* <div className="position: absolute">{inViewport ? "in" : "out"} ---- {scramble ? "s" : "ns"}</div> */}
      <img id={`image_${ind}`} src="/loading.jpg" className="z-index: 1" />
    </div>
  );
};

const ViewportBlock = handleViewport(Block, { rootMargin: "10px 0px 10px 0px" });

export default function ViewPort({ imageUrl, ind }: { imageUrl: string, ind: number }) {

  return (
    <ViewportBlock imageUrl={imageUrl} ind={ind} />
  )
}