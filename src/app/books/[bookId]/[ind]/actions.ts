'use server'

import prisma from '@/app/lib/prisma';
import { publish } from '@/app/lib/redis';

const RedisViewChannel = process.env.REDIS_VIEW_CHANNEL!;

export async function updateHistory(bookId: string, userId: string, chapterId: string, ind: number) {
  const existReadHistory = await prisma.readHistory.findFirst({
    where: { bookId: bookId, userId: userId, chapterId: chapterId },
  });

  if (existReadHistory) {
    await prisma.readHistory.update({
      where: { id: existReadHistory.id },
      data: {
        createdAt: new Date(),
      },
    });
  } else {
    await prisma.readHistory.create({
      data: {
        userId: userId,
        bookId: bookId,
        chapterId: chapterId,
      },
    });
  }


  const shelf = await prisma.bookShelf.findUnique({ where: { bookId_userId: { bookId: bookId, userId: userId } } });
  if (shelf) {
    await prisma.bookShelf.update({
      where: {
        id: shelf.id,
      },
      data: {
        bookId: bookId,
        latestChapterIndex: ind,
        chapterId: chapterId,
      },
    });
  }
}

export async function sendViewEvent(bookId: string, ind: string, sourceIP: string | null) {
  try {
    const viewEvent = {
      bookId: bookId,
      chapterIndex: ind,
      sourceIP: sourceIP,
    };

    await publish(RedisViewChannel, viewEvent);
  } catch (err) {
    console.error(err);
  }
}