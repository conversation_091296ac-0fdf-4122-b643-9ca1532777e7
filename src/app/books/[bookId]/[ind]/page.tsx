import Pagination from './pagination';
import { getBook } from "@/app/lib/pageUtil";
import { genImageUrls } from "@/app/lib/imageUrl";
import Viewport from "@/app/books/[bookId]/[ind]/viewport";
import { auth } from "@/auth";
import Script from 'next/script';
import { headers } from 'next/headers'
import AdsBanner from '@/app/components/AdsBanner';

import EventUpdater from "./eventUpdater";

export default async function ChapterPage({ params }: { params: { bookId: string, ind: string } }) {
  const book = await getBook(params.bookId);
  const activeResource = book?.activeResource;
  if (!book || !activeResource || !book.publish || book.archived) {
    return "error";
  }
  // const chapters = book?.activeResource?.chapters.filter(c => c.ind.toString() === params.ind);
  const chapters = book?.activeResource?.chapters;
  if (!chapters) {
    return "error";
  }

  const inds = chapters.map(c => c.ind);
  // console.log("inds:", inds);

  const current = Number(params.ind);
  const chapterInd = inds.at(current);
  // const total = chapters?.at(-1)?.ind!;
  const total = inds.length;
  // console.log("current;", current, "chapterInd:", chapterInd, "total:", total);
  const chapter = chapters?.find(chapter => chapter.ind == chapterInd);
  if (!chapter) {
    return "error";
  }
  // console.log("chapter:", chapter);

  const imagePaths = await genImageUrls(book?.id, activeResource?.resourceKey, chapter?.imagePaths);

  const session = await auth();
  const userId = session?.user?.id;

  // send view event
  const headersList = headers();
  const cfClientIP = headersList.get("cf-connecting-ip");
  // sendViewEvent(book.id, chapter.ind.toString(), cfClientIP);

  return (
    <div >
      <EventUpdater bookId={book.id} chapterId={chapter.id} userId={userId} ind={chapter.ind} sourceIP={cfClientIP} />
      <div className='px-1'>
        <div className="text-lg text-foreground flex justify-center">{book?.name}</div>
        <div className="text-foreground flex justify-center" >{chapter?.name}</div>
        <div className='text-muted-foreground text-right mr-4'>{current + 1}/{total}頁</div>
        <div className="my-2">
          <Pagination book_id={params.bookId} current={current} total={total} />
        </div>
      </div>

      <div className='grid grid-cols-2 gap-4 md:gap-8 my-2'>
        <div className='flex justify-center'>
          <AdsBanner id="ckujowsbm01220imk1znyd8gv" />
        </div>
        <div className='flex justify-center'>
          <AdsBanner id="ckunf3eoz00520illx0mrgoup" />
        </div>
      </div>

      <div className='max-w-full'>
        {imagePaths?.map((ip, ind) => <div className="flex justify-center" key={ind}>
          <Viewport imageUrl={ip} ind={ind} />
        </div>)}
      </div>

      <div className='grid grid-cols-2 gap-4 md:gap-8 my-2'>
        <div className='flex justify-center'>
          <AdsBanner id="cku648dka03810hmtesavoj98" />
        </div>
        <div className='flex justify-center'>
          <AdsBanner id="cku64ak6q04570hmtk40hmffi" />
        </div>
      </div>

      <div className='grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-8 my-2 sm:my-6'>
        <div className='flex flex-col items-center space-y-2'>
          <ins className="eas6a97888e" data-zoneid="4454466"></ins>
          <ins id="__clb-1854687" />
        </div>
        <div className='flex justify-center'>
          <ins id="901192" data-width="300" data-height="250"></ins>
        </div>
      </div>

      <div className='px-1'>
        <div className='text-muted-foreground text-right mr-4'>{current + 1}/{total}頁</div>
        <div className="my-2">
          <Pagination book_id={params.bookId} current={current} total={total} />
        </div>
      </div>

      {/* bottom float */}
      <Script
        type="text/javascript"
        data-cfasync="false"
        async
        src={`https://ra12.xyz/z/cku509rf304240hja6dnelzkx/code.js?t=${Math.floor(new Date().getTime() / 10000)}`}
        strategy="afterInteractive"
      />

      {/* Clickadu */}
      <Script
        data-cfasync="false"
        type="text/javascript"
        src="//chaseherbalpasty.com/lv/esnk/1854687/code.js"
        async
        id="__clb-1854687"
      />
    </div>
  )
}