'use client'

import React from "react";

import { updateHistory, sendViewEvent } from "./actions";

type Props = {
  bookId: string
  chapterId: string
  userId: string | undefined
  ind: number
  sourceIP: string | null
}

export default function EventUpdater({ bookId, chapterId, userId, ind, sourceIP }: Props) {
  const eventSent = React.useRef(false);

  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (!eventSent.current) {
        if (userId) {
          updateHistory(bookId, userId, chapterId, ind);
        }
        sendViewEvent(bookId, ind.toString(), sourceIP);
        eventSent.current = true;
      }
    }, 6000);

    return () => clearTimeout(timeoutId);
  }, [bookId, chapterId, userId, ind, sourceIP]);

  return (
    <></>
  )
}