'use client'

import {
  BookmarkIcon,
} from '@heroicons/react/24/outline'
import {
  BookmarkIcon as BookmarkIconSolid
} from '@heroicons/react/24/solid'

import { useState, useEffect } from 'react'
import { addToShelf, removeFromShelf } from '@/app/actions';

export default function AddShelfButton({ bookId, inShelf }: { bookId: string, inShelf: boolean }) {
  const [getInShelf, setInShelf] = useState(inShelf);

  const add = async (bookId: string) => {
    const response = await addToShelf(bookId);
    console.log("res:", response);
    if (response) {
      setInShelf(true);
    }
  }

  const remove = async (bookId: string) => {
    const response = await removeFromShelf(bookId);
    console.log("res:", response);
    if (response) {
      setInShelf(false);
    }
  }

  if (getInShelf) {
    return (
      <button
        type="button"
        className="inline-flex items-center gap-x-1.5 rounded-md bg-pink-600 px-3.5 py-2.5 text-lg font-semibold text-white shadow-sm hover:bg-pink-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-pink-700"
        onClick={() => remove(bookId)}
      >
        <BookmarkIconSolid aria-hidden="true" className="-ml-0.5 h-5 w-5" />
        已在書架
      </button>
    )

  } else {
    return (
      <button
        type="button"
        className="inline-flex items-center gap-x-1.5 rounded-md bg-pink-600 px-3.5 py-2.5 text-lg font-semibold text-white shadow-sm hover:bg-pink-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-pink-700"
        onClick={() => add(bookId)}
      >
        <BookmarkIcon aria-hidden="true" className="-ml-0.5 h-5 w-5" />
        放入書架
      </button>
    )

  }

}