import Image from 'next/image'
import { genCoverImageUrl } from "@/app/lib/imageUrl";
import { getBook } from '@/app/lib/pageUtil';
import prisma from '@/app/lib/prisma';
import { Book, Chapter } from '@prisma/client';
import AddShelfButton from './add-shelf-button';
import { auth } from "@/auth";
import { searchBooks } from "@/app/lib/meilisearch";
import ComicList from "@/app/components/comicList";
import Script from 'next/script';
import { getHotBooks } from '@/app/lib/pageUtil';
import ComicTile from '@/app/components/comicTile';
import { cache } from 'react';
import {
  EyeIcon,
  BookOpenIcon,
  ArrowPathRoundedSquareIcon,
} from '@heroicons/react/24/outline'
import { formatNumber } from "@/app/lib/pageUtil";
import AdsBanner from "@/app/components/AdsBanner";

const getBookShelf = cache(async (bookId: string, userId: string | undefined) => {
  if (!userId) return null;
  return prisma.bookShelf.findUnique({
    where: { bookId_userId: { bookId, userId } },
    include: { lastRead: true }
  });
});

const getBookData = cache(async (bookId: string) => {
  const [book, session] = await Promise.all([
    getBook(bookId),
    auth()
  ]);

  if (!book) return { book: null };

  const userId = session?.user?.id;

  const [bookShelf, hotBooks] = await Promise.all([
    getBookShelf(bookId, userId),
    getHotBooks(24, "20m")
  ]);

  return {
    book,
    lastRead: bookShelf?.lastRead,
    hotBooks
  };
});

const getRelatedBooksFromIds = cache(async (relatedBookIds: string[]) => {
  return prisma.book.findMany({ where: { id: { in: relatedBookIds } } });
});

const getRelatedBooks = cache(async (book: Book) => {
  const body = await searchBooks(book.name, 0, 15);

  let relatedBookIds: string[] = [];
  let relatedIds = body.hits.map((hit: any) => hit.id);
  for (let relatedId of relatedIds) {
    if (relatedId !== book.id) {
      relatedBookIds.push(relatedId);
    }
  }

  return getRelatedBooksFromIds(relatedBookIds);
});

export default async function BookPage({ params }: { params: { bookId: string } }) {
  const { book, lastRead, hotBooks } = await getBookData(params.bookId);

  if (!book) {
    return "error";
  }

  const chapters = book?.activeResource?.chapters;

  if (!chapters) {
    return "error";
  }

  const updatedAt = new Date(book?.updatedAt!).toLocaleDateString();
  const inds = chapters.map(c => c.ind);
  const chapterInd = lastRead ? inds.indexOf(lastRead?.ind) : null;
  const relatedBook = await getRelatedBooks(book);

  // Pre-compute reading button information
  const hasLastRead = !!lastRead;
  const readHref = hasLastRead ? `/books/${book?.id}/${chapterInd}` : `/books/${book?.id}/0`;
  const readLabel = hasLastRead ? `接上回 ${lastRead?.name}` : '開始閱讀';
  const colourClasses = hasLastRead
    ? 'bg-green-600 hover:bg-green-500 focus-visible:outline-green-700'
    : 'bg-blue-600 hover:bg-blue-500 focus-visible:outline-blue-700';

  return (
    <div className='px-1'>
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 md:gap-6">
        <div className="sm:col-span-2">
          <div className="flex flex-row gap-3 sm:gap-4">
            <div className="basis-2/5">
              <Image
                src={genCoverImageUrl(params.bookId)}
                width={300}
                height={500}
                alt={`${book?.name} cover`}
                className="rounded"
                priority
                sizes="(max-width: 640px) 40vw, 300px"
              />
            </div>
            <div className="basis-3/5 text-sm sm:text-base">
              <div className="text-xl text-foreground">{book?.name}</div>
              <div className="text-muted-foreground mb-2">
                <div>別名: {book?.alias.join(",  ")}</div>
              </div>
              <div>
                <div>作者: <span className='text-foreground'>{book?.author}</span></div>
              </div>
              <div>
                <div>狀態: <span className='text-foreground'>{book?.continued ? "連載中" : "已完結"}</span></div>
              </div>
              <div>
                <div>地區: <span className='text-foreground'>{book?.region}</span></div>
              </div>
              <div>
                <div>標籤: <span className='text-foreground'>{book?.tags.join(",")}</span></div>
              </div>
              <div className="flex justify-between items-center text-muted-foreground mt-2">
                <div className="flex items-center space-x-1">
                  <div>
                    <EyeIcon className="h-4 w-4" />
                  </div>
                  <div className="text-sm">
                    {formatNumber(book.viewCount)}
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <div>
                    <BookOpenIcon className="h-4 w-4" />
                  </div>
                  <div className="text-sm">
                    {formatNumber(book.likeCount)}
                  </div>
                </div>
              </div>
              <div className="text-sm text-muted-foreground">
                <div className="flex items-center space-x-1">
                  <div><ArrowPathRoundedSquareIcon className="h-4 w-4" /></div>
                  <div>{updatedAt}</div>
                </div>
              </div>
            </div>
          </div>
          <div className="my-2 text-foreground text-sm sm:text-base">
            <p>簡介:{book?.description}</p>
          </div>

          <div className='grid grid-cols-2 gap-4 md:gap-8 my-2'>
            <div className='flex justify-center'>
              <AdsBanner id="cku4o94lt00610ilpp431bhen" />
            </div>
            <div className='flex justify-center'>
              <AdsBanner id="cku544i6c00930hmw69z1ygd8" />
            </div>
          </div>

          <div className='flex justify-around space-x-1'>
            <AddShelfButton bookId={params.bookId} inShelf={lastRead ? true : false} />
            <a href={readHref}>
              <div
                className={`inline-flex items-center gap-x-1.5 rounded-md px-3.5 py-2.5 text-lg font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 ${colourClasses}`}
              >
                {readLabel}
              </div>
            </a>

          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 px-2 py-4">
            {chapters?.map((chapter: Chapter, ind: number) =>
              <a key={chapter.ind} href={`/books/${book?.id}/${ind}`}>
                <div className="text truncate bg-muted p-2 hover:bg-primary/10">{chapter.name}</div>
              </a>)}
          </div>

          <div className='grid grid-cols-2 gap-4 md:gap-8 my-2'>
            <div className='flex justify-center'>
              <AdsBanner id="cku87e55h03090hoeih0o9xvo" />
            </div>
            <div className='flex justify-center'>
              <AdsBanner id="clj2uisbx0001mp0irgxppil8" />
            </div>
          </div>
        </div>
        <div>
          <div className='text-foreground text-xl'>猜你喜歡</div>
          <div className='text-sm'>
            {relatedBook.map((book: Book, ind: number) =>
              ind == 2 ? <>
                <div id="ts_ad_native_1pgpq" className=' max-w-full'></div>
                <ComicList book={book} key={book.id} lastChapter={null} lastRead={null} showDate={book.createdAt} shortDate={true} />
              </> :
                <ComicList book={book} key={book.id} lastChapter={null} lastRead={null} showDate={book.createdAt} shortDate={true} />)
            }
          </div>
          <div className='max-w-full'>
            <ins className="eas6a97888e" data-zoneid="4454466"></ins>
          </div>
        </div>

      </div>

      <div className='flex justify-center my-2 max-w-full'>
        <ins id="901415" data-width="728" data-height="102"></ins>
      </div>

      <div>
        <div className='text-xl my-2 sm:my-4 text-foreground'>大家正在看</div>
        <div className='grid grid-cols-1 sm:grid-cols-4 md:grid-cols-6 gap-2 sm:gap-4'>
          {hotBooks.map((book: any, ind: number) => {
            const chapters = book.activeResource?.chapters;
            const lastChapter = chapters.at(-1);
            if (ind == 12) {
              return (
                <>
                  <div className=' col-span-1 sm:col-span-4 md:col-span-6 grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-8 my-2'>
                    <div className='flex justify-center'>
                      <ins id="__clb-1854687" />
                    </div>
                    <div className='flex justify-center'>
                      <iframe width="300" height="100" frameBorder="0" scrolling="no" src="//tsyndicate.com/iframes2/0b2331d821104f7ba689447a79d95971.html?"></iframe>
                    </div>
                  </div>
                  <ComicTile book={book} key={book.id} lastChapter={lastChapter} lastRead={null} />
                </>
              )
            } else {
              return (
                <ComicTile book={book} key={book.id} lastChapter={lastChapter} lastRead={null} />
              )
            }
          })}
        </div>
      </div>

      {/* bottom float */}
      <Script
        type="text/javascript"
        data-cfasync="false"
        async
        src={`https://ra12.xyz/z/cku509rf304240hja6dnelzkx/code.js?t=${Math.floor(new Date().getTime() / 10000)}`}
        strategy="afterInteractive"
      />

      {/* traffic stars */}
      <Script async src="/js/tm.js" strategy="afterInteractive"></Script>
      <Script
        data-cfasync="false"
        type="text/javascript"
        src="//cdn.tsyndicate.com/sdk/v1/master.spot.js"
        async
      />

      {/* Clickadu */}
      <Script
        data-cfasync="false"
        type="text/javascript"
        src="//chaseherbalpasty.com/lv/esnk/1854687/code.js"
        async
        id="__clb-1854687"
      />
    </div>
  )
}