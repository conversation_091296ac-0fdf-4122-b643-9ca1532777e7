'use client'

import { SignInButton } from '@/app/components/signIn';
import { useSession } from "next-auth/react"
import { Card, CardContent } from "@/components/ui/card"

// this page is used for mobile compability only
export default function SignInPage() {
  const { data: session } = useSession();

  return (
    <div className='container mx-auto px-4 py-6 min-h-[50vh] flex items-center justify-center'>
      <Card className="max-w-md w-full">
        <CardContent className="pt-6 space-y-4">
          {session && session.user && (
            <div className='space-y-2 text-center'>
              <div className='text-foreground'>您已經登入成功</div>
              <div className='text-foreground'>{session.user.email}</div>
            </div>
          )}
          {!session && (
            <div className='space-y-6 text-center'>
              <SignInButton />
              <div className='text-foreground'>
                我們已經<span className='text-destructive font-medium'>取消了一鍵登入</span>的功能, 請使用郵箱密碼登入
              </div>
              <div className='text-muted-foreground'>
                如果您從未使用密碼登入過，需要用之前的郵箱註冊帳戶並設置密碼，與此郵箱關聯的紀錄(如:書架)都會保留
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}