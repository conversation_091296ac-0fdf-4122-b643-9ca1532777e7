"use client"

import { SiteHeader } from "@/components/site-header"
import Footer from '@/app/components/footer'
import { SidebarProvider, useSidebar } from "@/components/sidebar-context"

function MainContent({ children }: { children: React.ReactNode }) {
  const { isCollapsed } = useSidebar()

  return (
    <div>
      <SiteHeader />
      <main className={`pt-2 sm:pt-4 transition-all duration-300 ${isCollapsed ? 'lg:pl-20' : 'lg:pl-72'}`}>
        <div className="px-0 sm:px-4 lg:px-8 pb-8">{children}</div>
        <Footer />
      </main>
    </div>
  )
}

export default function RootTemplate({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <SidebarProvider>
      <MainContent>{children}</MainContent>
    </SidebarProvider>
  )
}
