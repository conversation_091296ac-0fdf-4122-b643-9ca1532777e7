import prisma from '@/app/lib/prisma';
import <PERSON>rip<PERSON> from "next/script";
import { unstable_cache } from 'next/cache';
import ComicTile from "@/app/components/comicTile";
import { getHotBooks } from "@/app/lib/pageUtil";
import Cover from "@/app/components/cover";
import AdsText from "@/app/components/AdsText";
import AdsBanner from "@/app/components/AdsBanner";
import type { BookWithResource } from '@/app/types';

const itemPerPage = 6;

// Move this outside the map functions since it's being repeated
const renderBookTile = (book: BookWithResource, lastRead: any = null) => {
  const chapters = book.activeResource?.chapters || [];
  const lastChapter = chapters[chapters.length - 1] || null;
  return (
    <ComicTile
      book={book}
      key={book.id}
      lastChapter={lastChapter}
      lastRead={lastRead}
    />
  );
};

// Create separate cached functions for different time ranges
const getHourlyHotBooks = unstable_cache(
  async (limit: number) => {
    return getHotBooks(limit, "1h");
  },
  ['hot-books-hourly'],
  { revalidate: 60 }
);

const getDailyHotBooks = unstable_cache(
  async (limit: number) => {
    return getHotBooks(limit, "24h");
  },
  ['hot-books-daily'],
  { revalidate: 300 }
);

const getWeeklyHotBooks = unstable_cache(
  async (limit: number) => {
    return getHotBooks(limit, "7d");
  },
  ['hot-books-weekly'],
  { revalidate: 600 }
);

// Cache recent books data for 5 minutes
const getCachedRecentBooks = unstable_cache(
  async () => {
    return prisma.book.findMany({
      where: { publish: true },
      include: {
        activeResource: {
          include: {
            chapters: {
              orderBy: { ind: "desc" },
              take: 1,  // Only take the latest chapter
            },
          },
        },
      },
      orderBy: [{ updatedAt: "desc" }],
      take: itemPerPage * 2,
    });
  },
  ['recent-books'],
  { revalidate: 300 }
);

// Cache ended books count and data for 10 minutes since it changes less frequently
const getCachedEndedBooks = unstable_cache(
  async (skip: number) => {
    const [count, books] = await Promise.all([
      prisma.book.count({ where: { publish: true, continued: false } }),
      prisma.book.findMany({
        where: {
          publish: true,
          continued: false,
        },
        include: {
          activeResource: {
            include: {
              chapters: {
                orderBy: { ind: "desc" },
                take: 1,  // Only take the latest chapter
              },
            },
          },
        },
        take: itemPerPage * 2,
        skip,
      }),
    ]);
    return { count, books };
  },
  ['ended-books'],
  { revalidate: 600 }
);

export default async function HomePage() {
  // Fetch all data in parallel using cached functions
  const [hotBooks, dailyBooks, weeklyBooks, recentUpdatedBooks] = await Promise.all([
    getHourlyHotBooks(12),
    getDailyHotBooks(12),
    getWeeklyHotBooks(12),
    getCachedRecentBooks(),
  ]);

  // Get ended books with random skip
  const randomNum = Math.floor(Math.random() * 1000); // Cap at 1000 to avoid too large skips
  const { books: endedBooks } = await getCachedEndedBooks(randomNum);

  return (
    <div className='px-1'>
      <div className="bg-slate-100 dark:bg-slate-900/50 rounded-lg p-3 text-center">
        <div className="text-xl mb-2 text-gray-600 dark:text-gray-300">網址通告</div>
        <div className="grid grid-cols-1 space-x-1 space-y-1">
          <div className="mb text-gray-700 dark:text-gray-300">
            <a href="https://rdz1.xyz/dizhi" target="_blank" className="text-blue-600 dark:text-blue-400 underline">
              地址發布: https://rdz1.xyz/dizhi
            </a>
          </div>
          <div>
            <AdsText id="cm2jxoux70001oak7jqmsf46p" />
          </div>
          <div>
            <AdsText id="cm2jy0m2s0005oak7791yhesg" />
          </div>
        </div>
      </div>

      <div>
        <div className="text-center my-2">
          <div className="text-2xl text-gray-900 dark:text-gray-100">正熱門</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">當下超高人氣作品</div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 my-2">
          <div className='flex justify-center'>
            <AdsBanner id="ckunfc3nm01420hj9kqjfo2ea" />
          </div>
          <div className='flex justify-center'>
            <AdsBanner id="ckv6gba8c24350im3gr9wwg45" />
          </div>
        </div>

        <div className='grid grid-cols-1 sm:grid-cols-4 md:grid-cols-6 gap-2 sm:gap-4'>
          {hotBooks.map(book => renderBookTile(book))}
        </div>
      </div>

      <div className="my-8">
        <div className="text-center my-2">
          <div className="text-2xl text-gray-900 dark:text-gray-100">今日最佳</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">今日爆款</div>
        </div>


        <div className='grid grid-cols-1 sm:grid-cols-4 md:grid-cols-6 gap-2 sm:gap-4'>
          {dailyBooks.map(book => renderBookTile(book))}
        </div>
      </div>

      <div className="my-8">
        <div className="text-center my-2">
          <div className="text-2xl text-gray-900 dark:text-gray-100">最近更新</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">每日多次更新</div>
        </div>

        <div className="flex justify-center">
          <ins id="901415" data-width="728" data-height="102"></ins>
        </div>

        <div className='grid grid-cols-1 sm:grid-cols-4 md:grid-cols-6 gap-2 sm:gap-4'>
          {recentUpdatedBooks.map(book => renderBookTile(book))}
        </div>
      </div>

      <div className="my-8">
        <div className="text-center my-2">
          <div className="text-2xl text-gray-900 dark:text-gray-100">本週熱門</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">本週最熱漫畫</div>
        </div>

        <div className='grid grid-cols-2 gap-4 md:gap-8 my-2'>
          <div className='flex justify-center'>
            <AdsBanner id="cku5fxlu801090hmt8hzaoyt9" />
          </div>
          <div className='flex justify-center'>
            <AdsBanner id="cku6pwjfx05060hn3o8bjbj6j" />
          </div>
        </div>

        <div className='grid grid-cols-1 sm:grid-cols-4 md:grid-cols-6 gap-2 sm:gap-4'>
          {weeklyBooks.map(book => renderBookTile(book))}
        </div>
      </div>

      <div className="my-8">
        <div className="text-center my-2">
          <div className="text-2xl text-gray-900 dark:text-gray-100">已完結</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">完結精選</div>
        </div>

        <div className='grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8 my-2'>
          <div className='flex justify-center'>
            <AdsBanner id="ckv6h7kc601260hqi5faouh8t" />
          </div>
          <div className='flex justify-center'>
            <AdsBanner id="ckv6h7si902730hn4gdxxvbja" />
          </div>
          <div className='flex justify-center'>
            <AdsBanner id="ckv6h7zhl01500hqizfpijor9" />
          </div>
          <div className='flex justify-center'>
            <AdsBanner id="ckv6h89ek02970hn46ybhlao0" />
          </div>
        </div>

        <div className='grid grid-cols-1 sm:grid-cols-4 md:grid-cols-6 gap-2 sm:gap-4'>
          {endedBooks.map(book => renderBookTile(book))}
        </div>

        <div>
          <Cover url="https://ra12.xyz/z/clf7jci8h0001lt0i88nu0z2z/json" />
        </div>
      </div>
      <Script
        id="SCSpotScript"
        src="https://go.reebr.com/smartpop/0339b0f9d0de3f4d0f9f24de8593c5a0ef2b9e874b48ec12b638d343ee99bdb8?userId=71df7b2672bcea7e2d5425c79830b46adca4e9dd3e05f6bd5cdfc6c0d2287c40"
      />
      <Script src="/js/stripchat.js" />
    </div>
  )
}