import { auth } from "@/auth";
import type { Metadata } from "next";
import "./globals.css";
import { SessionProvider } from "next-auth/react"
import { ThemeProvider } from "@/components/theme-provider"


export const metadata: Metadata = {
  title: "肉漫屋-漫畫吧,漫畫大全,韓漫,肉漫免費在線觀看",
  description: "肉漫屋是免费的在线阅读网站，提供最新的高品质无遮韩国漫画。保证高清晰图片，高频率更新！",
};

export const revalidate = 300 // revalidate the data at most 5 mins

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await auth();
  return (
    <html lang="zh-Hant" suppressHydrationWarning>
      <head />
      <body>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <SessionProvider basePath={"/api/auth"} session={session}>
            {children}
          </SessionProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
