import { ArrowLongLeftIcon, ArrowLongRightIcon } from '@heroicons/react/20/solid'
import Link from 'next/link';

export default function Pagination({ path, current, total }: { path: string, current: number, total: number }) {

  const pre = current - 1;
  const pre2 = current - 2;
  const pre3 = current - 3;
  // const next = current == total - 1 ? current : current + 1;
  const next = current + 1;
  const next2 = current + 2;
  const next3 = current + 3;

  return (
    <nav className="flex items-center justify-between border-t border-border px-4 sm:px-0">
      <div className="-mt-px flex w-0 flex-1">
        <Link href={`/${path}&page=${pre >= 0 ? pre : 0}`} className="inline-flex items-center border-t-2 border-transparent pr-1 pt-4 text-sm font-medium text-foreground hover:border-primary/50 hover:text-foreground">
          <ArrowLongLeftIcon aria-hidden="true" className="mr-3 h-5 w-5 text-muted-foreground" />
          上一頁
        </Link>
      </div>
      <div className="md:hidden -mt-px flex w-0 justify-center">
        <div className="inline-flex items-center border-t-2 border-primary px-4 pt-4 text-sm font-medium text-primary">
          {current}
        </div>
      </div>
      <div className="hidden md:-mt-px md:flex">
        {pre3 > 0 && (
          <>
            <Link href={`/${path}&page=0`}
              className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-muted-foreground hover:border-border hover:text-foreground">
              {0}
            </Link>
            <span className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-muted-foreground">
              ...
            </span>
          </>
        )}

        {pre3 >= 0 && (
          <Link href={`/${path}&page=${pre3}`}
            className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-muted-foreground hover:border-border hover:text-foreground">
            {pre3}
          </Link>
        )}
        {pre2 >= 0 && (
          <Link href={`/${path}&page=${pre2}`}
            className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-muted-foreground hover:border-border hover:text-foreground">
            {pre2}
          </Link>
        )}
        {pre >= 0 && (
          <Link href={`/${path}&page=${pre}`}
            className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-muted-foreground hover:border-border hover:text-foreground">
            {pre}
          </Link>
        )}
        {/* Current: "border-primary text-primary", Default: "border-transparent text-muted-foreground hover:text-foreground hover:border-border" */}
        {current != null && (
          <Link href={`/${path}&page=${current}`}
            aria-current="page"
            className="inline-flex items-center border-t-2 border-primary px-4 pt-4 text-sm font-medium text-primary">
            {current}
          </Link>
        )}
        {next <= total - 1 && (
          <Link href={`/${path}&page=${next}`}
            className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-muted-foreground hover:border-border hover:text-foreground">
            {next}
          </Link>
        )}
        {next2 <= total - 1 && (
          <Link href={`/${path}&page=${next2}`}
            className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-muted-foreground hover:border-border hover:text-foreground">
            {next2}
          </Link>
        )}
        {next3 <= total - 1 && (
          <Link href={`/${path}&page=${next3}`}
            className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-muted-foreground hover:border-border hover:text-foreground">
            {next3}
          </Link>
        )}

        {total - 1 > next3 && (
          <>
            <span className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-muted-foreground">
              ...
            </span>
            <Link href={`/${path}&page=${total - 1}`}
              className="inline-flex items-center border-t-2 border-transparent px-4 pt-4 text-sm font-medium text-muted-foreground hover:border-border hover:text-foreground">
              {total - 1}
            </Link>
          </>
        )}
      </div>
      <div className="-mt-px flex w-0 flex-1 justify-end">
        <Link href={`/${path}&page=${next > total - 1 ? total - 1 : next}`} className="inline-flex items-center border-t-2 border-transparent pl-1 pt-4 text-sm font-medium text-foreground hover:border-primary/50 hover:text-foreground">
          下一頁
          <ArrowLongRightIcon aria-hidden="true" className="ml-3 h-5 w-5 text-muted-foreground" />
        </Link>
      </div>
    </nav>
  )
}
