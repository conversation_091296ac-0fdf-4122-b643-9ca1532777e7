'use client'

import React from "react";

export default function TextAds({ id }: { id: string }) {
  const [textAd, setTextAd] = React.useState<any>({});
  React.useEffect(() => {

    if (id) {
      // get text ads
      fetch(`https://ra12.xyz/z/${id}/json`)
        .then(resp => resp.json())
        .then(json => {
          // console.log("json:", json)
          const obj = json[0];
          if (obj) {
            setTextAd(obj);
          }
        })
    }
  }, [id]);

  return (
    <div className="hover:underline text-red-500 font-semibold"><a href={textAd.link_url} target="_blank" rel="noreferrer">{textAd.name}</a></div>
  )
}