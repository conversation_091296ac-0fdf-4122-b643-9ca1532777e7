import { Book, Chapter } from "@prisma/client";
import Link from 'next/link';
import { genCoverImageUrl } from "@/app/lib/imageUrl";
import {
  EyeIcon,
  BookOpenIcon,
  ArrowPathRoundedSquareIcon
} from '@heroicons/react/24/outline'
import { formatNumber } from "@/app/lib/pageUtil";

export default function ComicTile({ book, lastRead, lastChapter }: { book: Book, lastRead: Chapter | null, lastChapter: Chapter | null }) {
  const updatedAt = new Date(book.updatedAt!).toLocaleDateString();
  const coverImageUrl = genCoverImageUrl(book.id);

  // const linkUrl = lastRead ? `/books/${book.id}/${lastRead.ind}` : `books/${book.id}`;
  const linkUrl = `/books/${book.id}`;
  return (
    <div>
      <a href={linkUrl}>
        <div className="sm:hidden">
          <div className="grid grid-cols-3 gap-2">
            <div className="rounded-sm bg-no-repeat bg-top bg-cover pt-[65%] " style={{
              backgroundImage: `url("${coverImageUrl}")`,
            }}></div>
            <div className="col-span-2">
              <div className="truncate text-sm md:text-base text-foreground">{book.name}</div>
              {lastRead && (
                <div className="text-muted-foreground text-xs">讀: {lastRead.name}</div>
              )}
              {lastChapter && (
                <div className="text-muted-foreground text-xs">至: {lastChapter.name}</div>
              )}

              <div className="flex justify-between items-center mt-2">
                {book.viewCount != undefined && (
                  <div className="text-xs text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <div>
                        <EyeIcon className="h-4 w-4" />
                      </div>
                      <div>
                        {formatNumber(book.viewCount)}
                      </div>
                    </div>
                  </div>
                )}
                {book.likeCount != undefined && (
                  <div className="text-xs text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <div>
                        <BookOpenIcon className="h-4 w-4" />
                      </div>
                      <div>
                        {formatNumber(book.likeCount)}
                      </div>
                    </div>
                  </div>
                )}
                <div className="text-xs text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <div><ArrowPathRoundedSquareIcon className="h-4 w-4" /></div>
                    <div>{updatedAt}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="hidden sm:block border rounded-xl">
          <div className="rounded-xl bg-no-repeat bg-center bg-cover pt-[149%] " style={{
            backgroundImage: `url("${coverImageUrl}")`,
          }}></div>
          <div className="px-2 py-2">
            <div className="truncate text-foreground">{book.name}</div>
            {lastRead && (
              <div className="text-muted-foreground text-sm">讀: {lastRead.name}</div>
            )}
            {lastChapter && (
              <div className="text-muted-foreground text-sm">至: {lastChapter.name}</div>
            )}
            <div className="flex justify-between items-center">
              {book.viewCount != undefined && (
                <div className="text-muted-foreground">
                  <div className="flex items-center space-x-1 justify-end">
                    <div>
                      <EyeIcon className="h-4 w-4" />
                    </div>
                    <div className="text-sm">
                      {formatNumber(book.viewCount)}
                    </div>
                  </div>
                </div>
              )}
              {book.likeCount != undefined && (
                <div className="text-muted-foreground">
                  <div className="flex items-center space-x-1 justify-end">
                    <div>
                      <BookOpenIcon className="h-4 w-4" />
                    </div>
                    <div className="text-sm">
                      {formatNumber(book.likeCount)}
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="text-sm text-muted-foreground">
              <div className="flex items-center space-x-1 justify-end">
                <div><ArrowPathRoundedSquareIcon className="h-4 w-4" /></div>
                <div>{updatedAt}</div>
              </div>
            </div>
          </div>
        </div>
      </a>
    </div >
  )
}