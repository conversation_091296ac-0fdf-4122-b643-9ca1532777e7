import Link from "next/link"

export default function Avatar({ email }: { email: string }) {
  return (
    <Link
      href="/profile"
      className="flex items-center gap-x-4 text-sm font-semibold leading-6 text-foreground hover:bg-muted/50 rounded-lg p-2"
    >
      <span className="flex h-9 w-9 shrink-0 items-center justify-center rounded-lg border border-border bg-muted text-lg font-medium text-foreground">
        {email![0]}
      </span>
      <span aria-hidden="true" className="hidden sm:block">{email}</span>
    </Link>
  )
}
