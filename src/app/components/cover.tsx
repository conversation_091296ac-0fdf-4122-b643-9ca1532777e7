'use client'

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogPortal,
} from "@/components/ui/dialog"
import { XMarkIcon } from '@heroicons/react/24/outline';
import { setCookie, getCookie } from 'cookies-next';

export default function Cover({ url }: { url: string }) {
  const [open, setOpen] = useState(false)
  const [coverObj, setCoverObj] = useState<any>({});

  useEffect(() => {
    const coverClicked = getCookie("cover");
    if (!coverClicked) {
      fetch(url)
        .then(resp => resp.json())
        .then(json => {
          // console.log("json:", json)
          const obj = json[0];
          setCoverObj({ img: obj.image_url, url: obj.link_url });
          setOpen(true);

          // auto close ads after 10 sec
          setTimeout(() => {
            setOpen(false);
          }, 12000);
        })
    }


  }, []);

  const modalClose = () => {
    setCookie("cover", 1, { maxAge: 60 * 10 }); // 10 minutes
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={() => { }}>
      <DialogPortal>
        <DialogContent className="sm:max-w-sm lg:max-w-md p-2 [&>button]:hidden z-[2000001]">
          <div>
            <div onClick={modalClose} className='absolute right-1 top-1 hover:bg-pink-50 dark:hover:bg-pink-400 cursor-pointer'>
              <XMarkIcon className='h-8 w-8' />
            </div>
            <a href={coverObj.url} target="_blank" rel="noreferrer" onClick={modalClose}>
              <img src={coverObj.img} width={"100%"} className='rounded-sm' />
            </a>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  )
}