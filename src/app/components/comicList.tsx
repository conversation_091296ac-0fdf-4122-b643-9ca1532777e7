import { Book, Chapter } from "@prisma/client";
import Link from 'next/link';
import { genCoverImageUrl } from "@/app/lib/imageUrl";

export default function ComicList({ book, lastRead, lastChapter, showDate, shortDate }:
  { book: Book, lastRead: Chapter | null, lastChapter: Chapter | null, showDate: Date | null, shortDate: boolean }) {
  const readAt = shortDate ? new Date(showDate!).toLocaleDateString() : new Date(showDate!).toLocaleString();
  const coverImageUrl = genCoverImageUrl(book.id);

  const linkUrl = lastRead ? `/books/${book.id}/${lastRead.ind}` : `/books/${book.id}`;
  return (
    <div className=" w-full lg:w-96 my-2">
      <a href={linkUrl}>
        <div className="grid grid-cols-5 gap-2">
          <div className="col-span-2 rounded-sm bg-no-repeat bg-top bg-cover pt-[65%] " style={{
            backgroundImage: `url("${coverImageUrl}")`,
          }}></div>
          <div className="col-span-3">
            <div className="truncate text-foreground">{book.name}</div>
            {lastRead && (
              <div className="text-muted-foreground text-xs">讀: {lastRead.name}</div>
            )}
            {lastChapter && (
              <div className="text-muted-foreground text-xs">至: {lastChapter.name}</div>
            )}
            {readAt && (
              <div className="text-xs text-muted-foreground float-right">{readAt}</div>
            )}
          </div>
        </div>
      </a>
    </div >
  )
}