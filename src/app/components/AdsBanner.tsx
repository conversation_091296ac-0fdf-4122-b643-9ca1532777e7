
'use client'

import React from "react"

// Track loaded scripts per component instance
export default function AdsBanner({ id }: { id: string }) {
  // Use a ref to track if this specific instance has loaded the script
  const hasLoaded = React.useRef(false);

  React.useEffect(() => {
    if (!id || hasLoaded.current) return;

    // Set loaded flag to prevent double loading in strict mode
    hasLoaded.current = true;

    console.log("loading ads", id);
    const script = document.createElement("script");
    script.src = `https://ra12.xyz/z/${id}/code.js?t=${Math.floor(new Date().getTime() / 10000)}`;
    script.async = true;
    script.type = "text/javascript";
    script.setAttribute('data-ad-id', id);

    document.body.appendChild(script);

    // Cleanup on unmount
    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [id]); // Only depend on id

  return (
    <ins id={id} />
  )
}