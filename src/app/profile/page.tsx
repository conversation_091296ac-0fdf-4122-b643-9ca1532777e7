"use client"

import { useSession } from "next-auth/react"
import { SignOutButton } from "@/app/components/signOut";
import { UserIcon } from "@heroicons/react/24/outline";
import { Card, CardContent, CardHeader } from "@/components/ui/card"

export default function ProfilePage() {
  const { data: session, status } = useSession();

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center space-x-2 text-xl mb-8 text-foreground">
        <UserIcon className="h-7 w-7 shrink-0" />
        <div className="font-medium">個人資料</div>
      </div>

      {status === "loading" ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <div className="text-foreground">載入中...</div>
        </div>
      ) : !session ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <div className="text-foreground">請先登錄</div>
        </div>
      ) : (
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <h2 className="text-lg font-semibold text-foreground">帳戶信息</h2>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium leading-6 text-foreground">
                用戶名
              </label>
              <div className="mt-2">
                <input
                  id="name"
                  name="name"
                  disabled
                  className="block w-full rounded-md border-0 py-2 px-3 text-foreground shadow-sm ring-1 ring-input placeholder:text-muted-foreground focus:ring-2 focus:ring-inset focus:ring-primary disabled:cursor-not-allowed disabled:bg-muted disabled:text-muted-foreground sm:text-sm sm:leading-6"
                  defaultValue={session.user?.name!}
                />
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium leading-6 text-foreground">
                郵箱
              </label>
              <div className="mt-2">
                <input
                  id="email"
                  name="email"
                  disabled
                  className="block w-full rounded-md border-0 py-2 px-3 text-foreground shadow-sm ring-1 ring-input placeholder:text-muted-foreground focus:ring-2 focus:ring-inset focus:ring-primary disabled:cursor-not-allowed disabled:bg-muted disabled:text-muted-foreground sm:text-sm sm:leading-6"
                  defaultValue={session.user?.email!}
                />
              </div>
            </div>

            <div className="pt-4">
              <SignOutButton />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
