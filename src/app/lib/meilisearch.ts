import { Mei<PERSON>Search } from 'meilisearch'

let meiliClient: Mei<PERSON>Search | null = null;

const getMeiliClient = () => {
    if (!meiliClient) {
        const MEILI_URL = process.env.MEILI_URL
        const MEILI_API_KEY = process.env.MEILI_API_KEY

        if (!MEILI_URL) {
            throw new Error('MEILI_URL is not defined')
        }

        meiliClient = new MeiliSearch({
            host: MEILI_URL,
            apiKey: MEILI_API_KEY
        })
    }
    return meiliClient
}

const MEILI_BOOK_INDEX = process.env.MEILI_BOOK_INDEX

// save object to meilisearch index
interface IndexDoc {
    id: string;
    [key: string]: any;
}

export const saveObjectIndex = async (doc: IndexDoc) => {
    if (!doc.id) {
        throw new Error("no doc id");
    }
    console.log("going to update index:", doc.id);

    await getMeiliClient().index(MEILI_BOOK_INDEX!).addDocuments([doc])
};

export const deleteObjectIndex = async (docId: string) => {
    console.log("going to remove index:", docId);
    await getMeiliClient().index(MEILI_BOOK_INDEX!).deleteDocument(docId)
};

export const searchBooks = async (term: string, page: number, limit: number) => {
    if (page < 0) {
        throw new Error("page can not be negative");
    }
    console.log("search:", term);

    return await getMeiliClient().index(MEILI_BOOK_INDEX!).search(term, {
        offset: page * limit,
        limit: limit,
        attributesToSearchOn: ['name', 'alias', 'author', 'description']
    });
};

