import prisma from '@/app/lib/prisma';
// import { hotBooks } from '@/app/lib/elasticsearch';
import { hotBooks, Duration } from '@/app/lib/clickhouse';
import type { Book } from '@prisma/client';
import type { Session } from 'next-auth';
import type { BookWithResource, HotBooksResponse } from '@/app/types';
import { cache } from 'react';

export const getBook = cache(async (bookId: string) => {
  return prisma.book.findUnique({
    where: { id: bookId },
    include: { activeResource: { include: { chapters: { where: { active: true }, orderBy: { ind: 'asc' } } } } }
  })
})

export async function getHotBooks(limit: number, timeRange: Duration): Promise<BookWithResource[]> {
  const result = await hotBooks(limit, timeRange) as HotBooksResponse;

  const bookIds = result.aggregations?.hotBook?.buckets?.map((bucket) => bucket.key) || [];

  const books = await prisma.book.findMany({
    where: {
      id: {
        in: bookIds
      }
    },
    include: {
      activeResource: {
        include: {
          chapters: {
            orderBy: { ind: "desc" },
            take: 1,  // Only take the latest chapter
          }
        }
      }
    }
  });

  // Sort books according to the order in bookIds
  return bookIds
    .map((id: string) => books.find((book: Book) => book.id === id))
    .filter((book): book is BookWithResource => book !== undefined);
}

export const formatNumber = (num: number) => {
  // Handle invalid inputs
  if (num === null || num === undefined || isNaN(num)) {
    return '0';
  }

  // Convert to number if string
  const number = typeof num === 'string' ? parseFloat(num) : num;

  const lookup = [
    { value: 1e9, symbol: 'B' },
    { value: 1e6, symbol: 'M' },
    { value: 1e3, symbol: 'K' },
  ];

  const item = lookup.find(item => Math.abs(number) >= item.value);

  if (!item) {
    return number.toLocaleString();
  }

  const formattedNumber = (number / item.value).toFixed(1).replace(/\.0$/, '');
  return `${formattedNumber}${item.symbol}`;
};

export const isAdmin = (session: Session | null) => {
  if (session && session.user && session.user.roles?.includes("admin")) {
    return true;
  }
  return false;
};

export async function searchByTagAndComplete(
  page: number,
  itemsPerPage: number,
  tag: string,
  continued: boolean | undefined,
) {
  // Build where clause based on parameters
  const where: any = { publish: true, archived: false };

  if (continued !== undefined) {
    where.continued = continued;
  }

  if (tag) {
    where.tags = {
      some: {
        name: tag
      }
    };
  }

  // Get total count for pagination
  const total = await prisma.book.count({
    where
  });

  // Get paginated results
  const books = await prisma.book.findMany({
    where,
    skip: page * itemsPerPage,
    take: itemsPerPage,
    orderBy: {
      updatedAt: 'desc'
    },
    select: {
      id: true,
      name: true,
      updatedAt: true,
      viewCount: true,
      likeCount: true,
      activeResource: {
        select: {
          id: true,
          chapters: {
            select: {
              id: true,
              ind: true,
              name: true,
            },
            orderBy: { ind: "asc" },
          },
        },
      },
    }
  });

  // Format response to match previous elasticsearch structure
  return {
    total: total,
    books: books,
  };
}