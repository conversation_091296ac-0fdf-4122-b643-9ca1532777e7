import { createClient } from "redis";

const REDIS_READ_URL = process.env.REDIS_READ_URL;
const REDIS_WRITE_URL = process.env.REDIS_WRITE_URL;

const redisReadClient = createClient({
  url: REDIS_READ_URL,
});

const redisWriteClient = createClient({
  url: REDIS_WRITE_URL,
});

redisReadClient.on("error", (err) => {
  console.log(err);
});

redisWriteClient.on("error", (err) => {
  console.log(err);
});

const initClient = async () => {
  if (!redisReadClient.isOpen) {
    console.log("connect redis read client");
    await redisReadClient.connect();
  }
  if (!redisWriteClient.isOpen) {
    console.log("connect redis write client");
    await redisWriteClient.connect();
  }
};

export const readFromCache = async (key: string) => {
  await initClient();
  return redisReadClient.get(key);
};

export const writeToCache = async (key: string, fileContent: string, expire: number) => {
  await initClient();
  return redisWriteClient.setEx(key, expire, fileContent);
};

export const deleteCache = async (key: string) => {
  await initClient();
  return redisWriteClient.del(key);
};

export const publish = async (channel: string, data: any) => {
  await initClient();

  return redisWriteClient.publish(channel, JSON.stringify(data));
}