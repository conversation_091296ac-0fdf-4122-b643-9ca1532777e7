import { PrismaClient } from "@prisma/client";

// Create singleton instances for both primary and replica
const prismaClientSingleton = () => {
    return new PrismaClient({
        datasources: {
            db: {
                url: process.env.DATABASE_PRIMARY_URL
            }
        }
    });
};

const prismaReplicaClientSingleton = () => {
    return new PrismaClient({
        datasources: {
            db: {
                url: process.env.DATABASE_REPLICA_URL
            }
        }
    });
};

declare global {
    var prisma: undefined | ReturnType<typeof prismaClientSingleton>;
    var prismaReplica: undefined | ReturnType<typeof prismaReplicaClientSingleton>;
}

const prisma = globalThis.prisma ?? prismaClientSingleton();
const prismaReplica = globalThis.prismaReplica ?? prismaReplicaClientSingleton();

if (process.env.NODE_ENV !== "production") {
    globalThis.prisma = prisma;
    globalThis.prismaReplica = prismaReplica;
}

// Create extended Prisma client with read replica support
const extendedPrisma = prisma.$extends({
    query: {
        $allModels: {
            async findUnique({ model, operation, args, query }) {
                return handleReadOperation(model, operation, args, query);
            },
            async findFirst({ model, operation, args, query }) {
                return handleReadOperation(model, operation, args, query);
            },
            async findMany({ model, operation, args, query }) {
                return handleReadOperation(model, operation, args, query);
            },
            async count({ model, operation, args, query }) {
                return handleReadOperation(model, operation, args, query);
            },
            async aggregate({ model, operation, args, query }) {
                return handleReadOperation(model, operation, args, query);
            },
            async groupBy({ model, operation, args, query }) {
                return handleReadOperation(model, operation, args, query);
            },
        },
    },
});

// Helper function to handle read operations with replica fallback
async function handleReadOperation(
    model: string,
    operation: string,
    args: any,
    query: (args: any) => Promise<any>
) {
    try {
        // Use read replica for read operations
        // Use type assertion for dynamic model access
        return await (prismaReplica as any)[model][operation](args);
    } catch (error: unknown) {
        console.warn('Read replica error, failing over to primary:', (error as Error).message);
        // Failover to primary database
        try {
            return await query(args);
        } catch (primaryError: unknown) {
            console.error('Primary database error:', (primaryError as Error).message);
            throw primaryError;
        }
    }
}

export default extendedPrisma;
