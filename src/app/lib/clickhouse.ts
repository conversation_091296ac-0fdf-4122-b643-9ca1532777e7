import { createClient } from '@clickhouse/client';

const CLICKHOUSE_URL = process.env.CLICKHOUSE_URL;
const CLICKHOUSE_USER = process.env.CLICKHOUSE_USER;
const CLICKHOUSE_PASSWORD = process.env.CLICKHOUSE_PASSWORD;
const CLICKHOUSE_DATABASE = process.env.CLICKHOUSE_DATABASE;

const client = createClient({
    url: CLICKHOUSE_URL,
    username: CLICKHOUSE_USER,
    password: CLICKHOUSE_PASSWORD,
    database: CLICKHOUSE_DATABASE,
});

export type Duration = '20m' | '1h' | '2h' | '24h' | '7d';

const getDurationInterval = (duration: Duration): string => {
    switch (duration) {
        case '20m':
            return 'INTERVAL 20 MINUTE';
        case '1h':
            return 'INTERVAL 1 HOUR';
        case '2h':
            return 'INTERVAL 2 HOUR';
        case '24h':
            return 'INTERVAL 24 HOUR';
        case '7d':
            return 'INTERVAL 7 DAY';
        default:
            return 'INTERVAL 1 HOUR';
    }
};

export const hotBooks = async (size: number, duration: Duration = '1h') => {
    const interval = getDurationInterval(duration);

    const query = `
    SELECT 
      book_id,
      sum(view_count) as view_count
    FROM view_events
    WHERE viewed_at >= now() - ${interval}
    GROUP BY book_id
    ORDER BY view_count DESC
    LIMIT ${size}
  `;

    const resultSet = await client.query({
        query,
        format: 'JSONEachRow',
    });

    const data = await resultSet.json();
    return {
        aggregations: {
            hotBook: {
                buckets: data.map((row: any) => ({
                    key: row.book_id,
                    doc_count: row.view_count,
                })),
            },
        },
    };
}; 