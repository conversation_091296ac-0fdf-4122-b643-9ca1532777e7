import createHmac from "create-hmac";

const s3ProxyUrls = process.env.S3PROXY_URL?.split(",");
const s3Bucket = process.env.S3_BUCKET!;
const s3ProxyKey = process.env.S3PROXY_KEY!;
const s3ProxySalt = process.env.S3PROXY_SALT!;
const imageScrambleRatio = parseInt(process.env.IMG_SCRAMBLE_RATIO!);

export const urlSafeBase64 = (url: Buffer | string) => {
  return Buffer.from(url).toString("base64").replace(/=/g, "").replace(/\+/g, "-").replace(/\//g, "_");
};

const hexDecode = (hex: string) => Buffer.from(hex, "hex");

export const sign = (target: string, secret: string, salt: string) => {
  const hmac = createHmac("sha256", hexDecode(secret));
  hmac.update(hexDecode(salt));
  hmac.update(target);
  return urlSafeBase64(hmac.digest());
};

export const genCoverImageUrl = (bookId: string) => {
  const resourcePath = `s3://${s3Bucket}/images/${bookId}/cover.jpg`;
  const encodedUrl = urlSafeBase64(resourcePath);

  const full_path = `/wm:0/sr:0/${encodedUrl}.jpg`;

  const signature = sign(full_path, s3ProxyKey, s3ProxySalt);

  const url = `${s3ProxyUrls![0]}/${signature}${full_path}`;

  return url;
}

export async function genImageUrls(bookId: string, resourceKey: string, imagePaths: string[] | undefined) {
  if (!imagePaths) {
    return [];
  }

  let imageUrls: string[] = [];
  for (let i = 0; i < imagePaths.length; i++) {
    const imagePath = imagePaths[i];
    const ext = getImageExtension(imagePath);

    const resourcePath = `s3://${s3Bucket}/images/${bookId}/${resourceKey}/${imagePath}`;
    const encodedUrl = urlSafeBase64(resourcePath);

    const scramble = isScramble(imagePath) ? 1 : 0;
    const watermark = i % imageScrambleRatio === 1 ? 2 : 0;
    const full_path = `/wm:${watermark}/sr:${scramble}/${encodedUrl}.${ext}`;

    const signature = sign(full_path, s3ProxyKey, s3ProxySalt);
    const ind = i % s3ProxyUrls!.length;

    const url = `${s3ProxyUrls![ind]}/${signature}${full_path}`;
    imageUrls.push(url);
  }

  return imageUrls;
}

function getImageExtension(url: string): string | null {
  const parts = url.split('.');
  return parts.length > 1 ? parts.pop()!.toLowerCase() : null;
}

export function isScramble(imageUrl: string) {
  let digest = stringToInt(imageUrl);
  digest = digest % 10; // just need the last digit

  return digest % imageScrambleRatio === 0;
}

function stringToInt(str: string): number {
  let hash = 5381;
  for (let i = 0; i < str.length; i++) {
    // bitwise left shift with 5 0s - this would be similar to
    // hash * 32 + hash = hash * 33
    hash = ((hash << 5) + hash) + str.charCodeAt(i);
  }
  // Convert to 32 bit integer
  return hash & 0x7FFFFFFF;
}