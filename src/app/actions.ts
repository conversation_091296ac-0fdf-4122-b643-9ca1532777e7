'use server'

import { auth, signIn } from "@/auth";
import prisma from '@/app/lib/prisma';


export async function addToShelf(bookId: string) {
  const session = await auth();

  if (!session) {
    await signIn("keycloak");
    return
  };

  const userId = session?.user?.id;
  if (!userId) {
    await signIn("keycloak");
    return
  }

  const bookInDb = await prisma.book.findUnique({
    where: { id: bookId },
    include: {
      activeResource: {
        include: {
          chapters: {
            orderBy: { ind: 'asc' }
          }
        }
      }
    }
  });

  if (!bookInDb) {
    throw new Error('can not find book')
  }

  const shelfInDb = await prisma.bookShelf.findUnique({ where: { bookId_userId: { userId: userId, bookId: bookId } } });
  if (!shelfInDb) {
    const firstChapter = bookInDb.activeResource?.chapters[0];
    if (!firstChapter) {
      throw new Error('no default chapter')
    };
    await prisma.bookShelf.create({
      data: {
        userId: userId,
        bookId: bookInDb.id,
        chapterId: firstChapter?.id,
      },
    });
    // const newShelf = new BookShelf(currentUser, bookInDb);
    // await db.em.persistAndFlush(newShelf);
  }

  return { message: 'ok' };
}

export async function removeFromShelf(bookId: string) {
  const session = await auth();

  if (!session) {
    await signIn("keycloak");
    return
  };

  const userId = session?.user?.id;
  if (!userId) {
    await signIn("keycloak");
    return
  }

  await prisma.bookShelf.delete({ where: { bookId_userId: { userId: userId, bookId: bookId } } });
  return { message: 'ok' };
}