
import { auth } from "@/auth";
import prisma from '@/app/lib/prisma';

// this is for mobile compability only
export async function GET(request: Request) {
  const session = await auth();

  if (!session || !session.user) {
    return Response.json({ msg: "Unauthorized" }, { status: 401 });
  };

  const userId = session.user.id;
  if (!userId) {
    return Response.json({ msg: "Unauthorized" }, { status: 401 });
  }

  const currentUser = await prisma.user.findUnique({ where: { id: userId } });
  return Response.json({ user: currentUser });
}
