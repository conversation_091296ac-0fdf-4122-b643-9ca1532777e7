@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 100% 99%;
    --foreground: 250 20% 10%;
    --card: 0 0% 100%;
    --card-foreground: 250 20% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 250 20% 10%;
    --primary: 320 95% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 250 95% 65%;
    --secondary-foreground: 0 0% 100%;
    --muted: 250 20% 95%;
    --muted-foreground: 250 20% 40%;
    --accent: 190 95% 55%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 90% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 250 20% 92%;
    --input: 250 20% 92%;
    --ring: 320 95% 60%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 250 20% 10%;
    --foreground: 220 100% 99%;
    --card: 250 20% 12%;
    --card-foreground: 220 100% 99%;
    --popover: 250 20% 12%;
    --popover-foreground: 220 100% 99%;
    --primary: 320 95% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 250 95% 65%;
    --secondary-foreground: 0 0% 100%;
    --muted: 250 20% 20%;
    --muted-foreground: 250 20% 80%;
    --accent: 190 95% 55%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 90% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 250 20% 20%;
    --input: 250 20% 20%;
    --ring: 320 95% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

.dark body {
  background-image:
    radial-gradient(at 27% 37%, hsla(320, 95%, 60%, 0.2) 0px, transparent 50%),
    radial-gradient(at 97% 21%, hsla(250, 95%, 65%, 0.2) 0px, transparent 50%),
    radial-gradient(at 52% 99%, hsla(190, 95%, 55%, 0.2) 0px, transparent 50%),
    radial-gradient(at 10% 29%, hsla(320, 95%, 60%, 0.2) 0px, transparent 50%),
    radial-gradient(at 97% 96%, hsla(250, 95%, 65%, 0.2) 0px, transparent 50%),
    radial-gradient(at 33% 50%, hsla(190, 95%, 55%, 0.2) 0px, transparent 50%);
  background-attachment: fixed;
}

body {
  background-image:
    radial-gradient(at 27% 37%, hsla(320, 95%, 60%, 0.1) 0px, transparent 50%),
    radial-gradient(at 97% 21%, hsla(250, 95%, 65%, 0.1) 0px, transparent 50%),
    radial-gradient(at 52% 99%, hsla(190, 95%, 55%, 0.1) 0px, transparent 50%),
    radial-gradient(at 10% 29%, hsla(320, 95%, 60%, 0.1) 0px, transparent 50%),
    radial-gradient(at 97% 96%, hsla(250, 95%, 65%, 0.1) 0px, transparent 50%),
    radial-gradient(at 33% 50%, hsla(190, 95%, 55%, 0.1) 0px, transparent 50%);
  background-attachment: fixed;
  min-height: 100vh;
}